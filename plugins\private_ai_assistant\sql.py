"""
私聊AI助手插件SQL函数
包含私聊AI助手所需的所有SQL查询函数
"""
from typing import Optional, List, Dict
from datetime import datetime, timedelta


def init_private_ai_tables(self):
    """初始化私聊AI助手相关表"""
    cursor = self.connection.cursor()

    # 创建私聊AI配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS private_ai_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_name VARCHAR(255) NOT NULL COMMENT '用户名称',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用AI助手',
            ai_agent_id BIGINT DEFAULT 4 COMMENT '使用的AI智能体ID，默认为4',
            context_limit INT DEFAULT 20 COMMENT '上下文条数限制',
            session_hours INT DEFAULT 24 COMMENT '会话时长（小时）',
            personality TEXT NULL COMMENT '个性化设置，JSON格式',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uk_user (user_name),
            INDEX idx_enabled (enabled),
            INDEX idx_agent_id (ai_agent_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建私聊会话统计表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS private_ai_sessions (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            user_name VARCHAR(255) NOT NULL COMMENT '用户名称',
            session_date DATE NOT NULL COMMENT '会话日期',
            message_count INT DEFAULT 0 COMMENT '消息数量',
            ai_reply_count INT DEFAULT 0 COMMENT 'AI回复数量',
            first_message_time DATETIME NULL COMMENT '首次消息时间',
            last_message_time DATETIME NULL COMMENT '最后消息时间',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uk_user_date (user_name, session_date),
            INDEX idx_user_name (user_name),
            INDEX idx_session_date (session_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_private_ai_config(self, user_name: str) -> Optional[Dict]:
    """获取用户的私聊AI配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM private_ai_config
        WHERE user_name = %s
    """, (user_name,))

    return cursor.fetchone()


def upsert_private_ai_config(self, user_name: str, enabled: bool = True, 
                           ai_agent_id: int = 4, context_limit: int = 20,
                           session_hours: int = 24, personality: str = None):
    """插入或更新私聊AI配置"""
    cursor = self.connection.cursor()

    cursor.execute("""
        INSERT INTO private_ai_config
        (user_name, enabled, ai_agent_id, context_limit, session_hours, personality)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            enabled = VALUES(enabled),
            ai_agent_id = VALUES(ai_agent_id),
            context_limit = VALUES(context_limit),
            session_hours = VALUES(session_hours),
            personality = VALUES(personality),
            updated_at = CURRENT_TIMESTAMP
    """, (user_name, enabled, ai_agent_id, context_limit, session_hours, personality))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_ai_agent_by_id(self, agent_id: int) -> Optional[Dict]:
    """获取指定ID的AI智能体配置"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        WHERE id = %s
    """, (agent_id,))
    return cursor.fetchone()


def get_default_ai_agent(self) -> Optional[Dict]:
    """获取默认AI智能体（ID为4）"""
    return self.get_ai_agent_by_id(4)


def update_session_stats(self, user_name: str, message_type: str = 'user'):
    """更新会话统计信息"""
    cursor = self.connection.cursor()
    
    today = datetime.now().date()
    now = datetime.now()
    
    if message_type == 'user':
        cursor.execute("""
            INSERT INTO private_ai_sessions 
            (user_name, session_date, message_count, first_message_time, last_message_time)
            VALUES (%s, %s, 1, %s, %s)
            ON DUPLICATE KEY UPDATE
                message_count = message_count + 1,
                last_message_time = %s,
                first_message_time = COALESCE(first_message_time, %s)
        """, (user_name, today, now, now, now, now))
    else:  # AI回复
        cursor.execute("""
            INSERT INTO private_ai_sessions 
            (user_name, session_date, ai_reply_count, last_message_time)
            VALUES (%s, %s, 1, %s)
            ON DUPLICATE KEY UPDATE
                ai_reply_count = ai_reply_count + 1,
                last_message_time = %s
        """, (user_name, today, now, now))
    
    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_user_session_stats(self, user_name: str, days: int = 7) -> List[Dict]:
    """获取用户最近的会话统计"""
    cursor = self.connection.cursor(dictionary=True)
    
    start_date = datetime.now().date() - timedelta(days=days)
    
    cursor.execute("""
        SELECT * FROM private_ai_sessions
        WHERE user_name = %s AND session_date >= %s
        ORDER BY session_date DESC
    """, (user_name, start_date))
    
    return cursor.fetchall()


def cleanup_old_sessions(self, days_to_keep: int = 90):
    """清理旧的会话统计记录"""
    cursor = self.connection.cursor()
    
    cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
    
    cursor.execute("""
        DELETE FROM private_ai_sessions
        WHERE session_date < %s
    """, (cutoff_date,))
    
    deleted_count = cursor.rowcount
    
    try:
        self.connection.commit()
        return deleted_count
    except Exception as e:
        self.connection.rollback()
        raise e


# 导出SQL函数字典
PRIVATE_AI_ASSISTANT_SQL_FUNCTIONS = {
    'init_private_ai_tables': init_private_ai_tables,
    'get_private_ai_config': get_private_ai_config,
    'upsert_private_ai_config': upsert_private_ai_config,
    'get_ai_agent_by_id': get_ai_agent_by_id,
    'get_default_ai_agent': get_default_ai_agent,
    'update_session_stats': update_session_stats,
    'get_user_session_stats': get_user_session_stats,
    'cleanup_old_sessions': cleanup_old_sessions,
}

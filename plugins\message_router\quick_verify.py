"""
消息路由插件快速验证脚本
用于快速测试消息路由功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from unittest.mock import Mock


def test_message_routing_logic():
    """测试消息路由逻辑"""
    print("=== 测试消息路由逻辑 ===")
    
    try:
        from plugins.message_router.plugin import MessageRouterPlugin
        
        # 创建模拟handler
        mock_handler = Mock()
        mock_handler._log = lambda msg, level="INFO": print(f"[{level}] {msg}")
        mock_handler.chat_type_cache = {
            'private_user': 'friend',
            'group_chat': 'group'
        }
        
        # 初始化插件
        router = MessageRouterPlugin(mock_handler)
        
        # 测试私聊检测
        assert router._is_private_chat('private_user') == True
        assert router._is_private_chat('group_chat') == False
        print("✓ 私聊检测功能正常")
        
        # 测试@消息检测
        test_cases = [
            (["@AI 你好"], True),
            (["AI，帮忙"], True),
            (["助手请回答"], True),
            (["今天天气不错"], False)
        ]
        
        for messages, expected in test_cases:
            result = router._is_at_message(messages)
            assert result == expected, f"@消息检测失败: {messages} -> {result}, 期望 {expected}"
        
        print("✓ @消息检测功能正常")
        
        # 测试消息分析
        analysis = router._analyze_message_type('private_user', ['你好'])
        assert analysis['is_private'] == True
        assert analysis['needs_private_ai'] == True
        
        analysis = router._analyze_message_type('group_chat', ['AI你好'])
        assert analysis['is_group'] == True
        assert analysis['needs_group_ai'] == True
        
        print("✓ 消息分析功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 消息路由逻辑测试失败: {e}")
        return False


def test_plugin_priority():
    """测试插件优先级设置"""
    print("\n=== 测试插件优先级设置 ===")
    
    try:
        # 检查消息路由插件优先级
        from plugins.message_router.plugin import MessageRouterPlugin
        assert MessageRouterPlugin.priority == 50
        print("✓ 消息路由插件优先级: 50")
        
        # 检查私聊AI插件优先级
        try:
            from plugins.private_ai_assistant.plugin import PrivateAIAssistantPlugin
            assert PrivateAIAssistantPlugin.priority == 150
            print("✓ 私聊AI插件优先级: 150")
        except ImportError:
            print("⚠️  私聊AI插件未找到")
        
        # 检查群聊历史插件优先级
        try:
            from plugins.conversation_history.plugin import ConversationHistoryPlugin
            assert ConversationHistoryPlugin.priority == 250
            print("✓ 群聊历史插件优先级: 250")
        except ImportError:
            print("⚠️  群聊历史插件未找到")
        
        return True
        
    except Exception as e:
        print(f"✗ 插件优先级测试失败: {e}")
        return False


def test_plugin_structure():
    """测试插件目录结构"""
    print("\n=== 测试插件目录结构 ===")
    
    base_path = os.path.dirname(__file__)
    
    required_files = [
        '__init__.py',
        'plugin.py',
        'test_message_routing.py',
        'README.md',
        'DEPLOYMENT_GUIDE.md'
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(base_path, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
        else:
            print(f"✓ {file_name} 存在")
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("✓ 插件目录结构完整")
        return True


def test_import_compatibility():
    """测试导入兼容性"""
    print("\n=== 测试导入兼容性 ===")
    
    try:
        # 测试消息路由插件导入
        from plugins.message_router import MessageRouterPlugin
        print("✓ 消息路由插件导入成功")
        
        # 测试其他插件导入
        try:
            from plugins.private_ai_assistant import PrivateAIAssistantPlugin
            print("✓ 私聊AI插件导入成功")
        except ImportError as e:
            print(f"⚠️  私聊AI插件导入失败: {e}")
        
        try:
            from plugins.conversation_history import ConversationHistoryPlugin
            print("✓ 群聊历史插件导入成功")
        except ImportError as e:
            print(f"⚠️  群聊历史插件导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入兼容性测试失败: {e}")
        return False


def test_message_patterns():
    """测试消息模式识别"""
    print("\n=== 测试消息模式识别 ===")
    
    try:
        from plugins.message_router.plugin import MessageRouterPlugin
        
        mock_handler = Mock()
        mock_handler._log = lambda msg, level="INFO": None
        mock_handler.chat_type_cache = {}
        
        router = MessageRouterPlugin(mock_handler)
        
        # 测试各种@消息模式
        at_test_cases = [
            "@AI 你好",
            "@助手 帮忙",
            "@机器人 回答问题",
            "@小明 AI能帮忙吗",
            "AI，你好",
            "助手，请帮忙",
            "请问，AI能做什么",
            "ai你好",
            "机器人帮我查一下"
        ]
        
        non_at_test_cases = [
            "今天天气不错",
            "大家好",
            "有人在吗",
            "晚安"
        ]
        
        # 测试应该触发的消息
        for message in at_test_cases:
            result = router._is_at_message([message])
            if result:
                print(f"✓ 正确识别: '{message}'")
            else:
                print(f"✗ 识别失败: '{message}'")
                return False
        
        # 测试不应该触发的消息
        for message in non_at_test_cases:
            result = router._is_at_message([message])
            if not result:
                print(f"✓ 正确过滤: '{message}'")
            else:
                print(f"✗ 误识别: '{message}'")
                return False
        
        print("✓ 消息模式识别功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 消息模式识别测试失败: {e}")
        return False


def main():
    """主函数"""
    print("消息路由插件快速验证")
    print("=" * 50)
    
    tests = [
        ("插件目录结构", test_plugin_structure),
        ("导入兼容性", test_import_compatibility),
        ("插件优先级设置", test_plugin_priority),
        ("消息路由逻辑", test_message_routing_logic),
        ("消息模式识别", test_message_patterns)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("验证结果总结:")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！消息路由插件已就绪")
        print("\n下一步:")
        print("1. 重启微信机器人服务")
        print("2. 同时发送私聊消息和群聊@消息测试")
        print("3. 观察日志确认并发处理正常")
        print("4. 运行完整测试: python plugins/message_router/test_message_routing.py")
    else:
        print(f"\n⚠️  {total - passed} 项验证失败，请解决相关问题")
        print("\n常见问题解决:")
        print("1. 插件目录结构不完整 -> 检查文件是否完整")
        print("2. 导入失败 -> 检查Python路径和依赖")
        print("3. 优先级设置错误 -> 检查插件priority属性")
        print("4. 消息识别异常 -> 检查正则表达式逻辑")


if __name__ == "__main__":
    main()

"""
对话记录存储插件
功能：
1. 存储用户对话记录到数据库
2. 查询上下文（默认100条）
3. 结合AI模型进行上下文回复
4. 支持1天为一个会话周期
5. 为不同用户维护独立的会话上下文
"""
import json
from typing import List, Optional, Dict
from datetime import datetime

from core.plugin_base import Plugin
from .sql import CONVERSATION_HISTORY_SQL_FUNCTIONS

# 导入AI模型
from models import (
    AIModel,
    GPT35TurboModel,
    DifyChatFlowModel,
    DifyWorkFlowModel,
    DifyAgentModel
)


class ConversationHistoryPlugin(Plugin):
    """对话记录存储插件 - 支持按用户分别记录和回复"""

    MODEL_MAP = {
        "gpt-3.5-turbo": GPT35TurboModel,
        "dify_chatflow": DifyChatFlowModel,
        "dify_workflow": DifyWorkFlowModel,
        "dify_agent": DifyAgentModel
    }

    priority = 200  # 较低优先级，在其他插件之后执行

    def __init__(self, handler):
        super().__init__(handler)

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("ConversationHistoryPlugin", CONVERSATION_HISTORY_SQL_FUNCTIONS)

        # 初始化数据库表
        try:
            self.db.init_conversation_tables()
            self.handler._log("✅ 对话记录表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ 初始化对话记录表失败: {e}", level="ERROR")

        # 缓存配置
        self.config_cache = {}
        self._load_configs()

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("ConversationHistoryPlugin")

    def _load_configs(self):
        """加载所有聊天的对话配置"""
        # 这里可以预加载一些默认配置，或者从数据库加载
        pass

    def _get_chat_config(self, chat_name: str, chat_type: str) -> Dict:
        """获取聊天配置，如果不存在则创建默认配置"""
        cache_key = f"{chat_name}_{chat_type}"

        if cache_key not in self.config_cache:
            config = self.db.get_conversation_config(chat_name, chat_type)

            if not config:
                # 创建默认配置
                self.db.upsert_conversation_config(
                    chat_name=chat_name,
                    chat_type=chat_type,
                    enabled=True,
                    context_limit=100,
                    session_hours=24,
                    ai_agent_id=None,
                    trigger_keywords=json.dumps(["AI", "ai", "助手", "机器人"])
                )
                config = {
                    'chat_name': chat_name,
                    'chat_type': chat_type,
                    'enabled': True,
                    'context_limit': 100,
                    'session_hours': 24,
                    'ai_agent_id': None,
                    'trigger_keywords': '["AI", "ai", "助手", "机器人"]'
                }

            self.config_cache[cache_key] = config

        return self.config_cache[cache_key]

    def _should_trigger_ai(self, messages: List[str], trigger_keywords: List[str]) -> bool:
        """判断是否应该触发AI回复"""
        if not trigger_keywords:
            return True  # 如果没有设置关键词，则总是触发

        for message in messages:
            for keyword in trigger_keywords:
                if keyword.lower() in message.lower():
                    return True
        return False

    def _build_context_prompt(self, context_messages: List[Dict], current_messages: List[str]) -> str:
        """构建包含历史上下文的提示词"""
        context_lines = []

        # 添加历史对话
        if context_messages:
            context_lines.append("=== 历史对话上下文 ===")
            for msg in context_messages:
                role = "用户" if msg['message_type'] == 'user' else "助手"
                sender = msg.get('sender', '未知')
                if msg['message_type'] == 'user':
                    context_lines.append(f"{role}({sender}): {msg['message_content']}")
                else:
                    context_lines.append(f"{role}: {msg['message_content']}")
            context_lines.append("=== 当前对话 ===")

        # 添加当前消息
        for msg in current_messages:
            context_lines.append(f"用户: {msg}")

        context_lines.append("\n请基于以上对话历史，回答当前对话用户的问题，然后给出合适的回复：")

        return "\n".join(context_lines)

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        处理消息 - 由于当前插件接口限制，这里只做基本的消息记录
        真正的用户区分处理在 handle_user_messages 方法中
        """
        if not messages:
            return None

        # 判断聊天类型
        chat_type = 'group'  # 默认为群聊
        if hasattr(self.handler, 'chat_type_cache'):
            chat_type = self.handler.chat_type_cache.get(chat, 'group')

        # 获取配置
        config = self._get_chat_config(chat, chat_type)

        if not config.get('enabled', True):
            return None

        # 由于插件接口限制，这里只能做简单的消息记录
        # 真正的用户区分处理需要在消息处理流程中调用专门的方法
        return None

    def handle_user_messages(self, chat: str, chat_type: str, user_messages: List[Dict]) -> Optional[str]:
        """
        处理带有用户信息的消息
        user_messages: [{"sender": "用户名", "content": "消息内容", "at_me": bool}]
        """
        if not user_messages:
            return None

        # 获取配置
        config = self._get_chat_config(chat, chat_type)

        if not config.get('enabled', True):
            return None

        try:
            # 解析触发关键词
            trigger_keywords = []
            if config.get('trigger_keywords'):
                trigger_keywords = json.loads(config['trigger_keywords'])
        except:
            trigger_keywords = ["AI", "ai", "助手", "机器人"]

        # 保存用户消息（按用户合并后保存）
        session_hours = config.get('session_hours', 24)
        user_grouped_messages = self._group_messages_by_user(user_messages)

        for sender, messages in user_grouped_messages.items():
            # 合并该用户的所有消息内容
            combined_content = " ".join([msg['content'] for msg in messages])

            # 保存合并后的消息
            self.db.save_message(
                chat_name=chat,
                chat_type=chat_type,
                sender=sender,
                message_content=combined_content,
                message_type='user',
                session_hours=session_hours
            )

        # 检查是否有用户触发AI回复
        triggered_users = []
        for sender, messages in user_grouped_messages.items():
            # 合并该用户的所有消息内容
            combined_content = " ".join([msg['content'] for msg in messages])

            if self._should_trigger_ai([combined_content], trigger_keywords):
                triggered_users.append({
                    'sender': sender,
                    'content': combined_content,
                    'messages': messages  # 保留原始消息列表
                })

        if not triggered_users:
            return None

        # 为每个触发AI的用户生成回复
        responses = []
        context_limit = config.get('context_limit', 100)

        for user_data in triggered_users:
            # 获取该用户的历史上下文
            user_context = self.db.get_user_conversation_context(chat, chat_type, user_data['sender'], context_limit, session_hours)

            # 构建包含用户上下文的提示词
            prompt = self._build_user_context_prompt(user_context, user_data)

            # 获取AI回复
            ai_response = self._get_ai_response(config, prompt)

            if ai_response:
                # 保存AI回复到数据库
                self.db.save_message(
                    chat_name=chat,
                    chat_type=chat_type,
                    sender="AI助手",
                    message_content=ai_response,
                    message_type='bot',
                    session_hours=session_hours
                )

                # 直接使用AI回复内容，让发送函数处理@格式
                responses.append(ai_response)

                self.handler._log(f"[对话记录] 【{chat}】为用户 {user_data['sender']} 生成AI回复，上下文条数: {len(user_context)}")

        # 返回合并的回复（如果有多个用户触发）
        if responses:
            # 清理回复内容，确保格式正确
            cleaned_responses = []
            for response in responses:
                # 移除多余的空白字符，但保留必要的换行
                cleaned_response = str(response).strip()
                if cleaned_response:
                    cleaned_responses.append(cleaned_response)

            if cleaned_responses:
                return "\n\n".join(cleaned_responses)

        return None

    def _group_messages_by_user(self, user_messages: List[Dict]) -> Dict[str, List[Dict]]:
        """将消息按用户分组，合并同一用户的连续消息"""
        grouped = {}
        for msg in user_messages:
            sender = msg['sender']
            if sender not in grouped:
                grouped[sender] = []
            grouped[sender].append(msg)
        return grouped

    def _build_user_context_prompt(self, context_messages: List[Dict], current_user_data: Dict) -> str:
        """构建包含用户历史上下文的提示词"""
        context_lines = []

        # 添加历史对话
        if context_messages:
            context_lines.append("=== 历史对话上下文 ===")
            for msg in context_messages:
                role = "助手" if msg['message_type'] == 'bot' else "用户"
                sender = msg.get('sender', '未知')
                if msg['message_type'] == 'user':
                    context_lines.append(f"{role}({sender}): {msg['message_content']}")
                else:
                    context_lines.append(f"{role}: {msg['message_content']}")
            context_lines.append("=== 当前对话 ===")

        # 添加当前用户消息（合并后的完整内容）
        context_lines.append(f"用户({current_user_data['sender']}): {current_user_data['content']}")

        context_lines.append(f"\n请基于以上对话历史，针对用户 {current_user_data['sender']} 的问题给出合适的回复：")

        return "\n".join(context_lines)

    def _save_user_messages(self, chat: str, chat_type: str, messages: List[str], config: Dict):
        """保存用户消息到数据库（简化版本，用于兼容旧接口）"""
        session_hours = config.get('session_hours', 24)

        for message in messages:
            self.db.save_message(
                chat_name=chat,
                chat_type=chat_type,
                sender="未知用户",  # 由于接口限制，无法获取真实发送者
                message_content=message,
                message_type='user',
                session_hours=session_hours
            )

    def _get_ai_response(self, config: Dict, prompt: str) -> Optional[str]:
        """获取AI回复"""
        ai_agent_id = config.get('ai_agent_id')

        if not ai_agent_id:
            # 如果没有配置特定的AI智能体，使用默认的
            self.handler._log("[对话记录] 未配置AI智能体，跳过AI回复")
            return None

        # 获取AI智能体配置
        agent_config = self.db.get_ai_agent_by_id(ai_agent_id)
        if not agent_config:
            self.handler._log(f"[对话记录] 未找到AI智能体配置 ID: {ai_agent_id}")
            return None

        model_type = agent_config.get("model_type", "gpt-3.5-turbo")
        model_class = self.MODEL_MAP.get(model_type)

        if not model_class:
            self.handler._log(f"[对话记录] 不支持的模型类型: {model_type}")
            return None

        try:
            # 实例化模型
            model_kwargs = {
                "api_key": agent_config["api_key"],
                "url": agent_config["url"]
            }

            ai_model: AIModel = model_class(**model_kwargs)
            ai_response = ai_model.generate(context=prompt)

            return ai_response
        except Exception as e:
            self.handler._log(f"[对话记录] AI模型调用失败: {e}", level="ERROR")
            return None

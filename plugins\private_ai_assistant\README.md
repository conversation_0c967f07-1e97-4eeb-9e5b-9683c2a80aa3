# 私聊AI助手插件

## 概述

私聊AI助手插件专门处理私聊消息，利用上下文历史记录提供智能回复。用户发送任何消息都会触发AI回复，无需关键词触发，默认使用智能体4。

## 核心特性

### 🎯 专门处理私聊
- **自动识别私聊**：只处理私聊消息，忽略群聊
- **无关键词触发**：用户发什么就回复什么
- **即时响应**：收到消息立即生成回复

### 🧠 智能上下文感知
- **历史记录利用**：基于conversation_history插件的历史记录
- **上下文构建**：自动包含最近的对话历史
- **个性化回复**：根据用户配置调整回复风格

### 🔧 默认使用智能体4
- **预配置智能体**：默认使用ID为4的AI智能体
- **灵活配置**：支持为不同用户配置不同智能体
- **模型支持**：支持多种AI模型类型

### 📊 完整数据管理
- **用户配置管理**：个性化设置存储
- **会话统计**：记录用户互动数据
- **历史记录存储**：完整的对话历史追踪

## 功能详解

### 1. 私聊消息处理

#### 自动触发机制
```python
# 用户发送任何消息都会触发AI回复
user_message = "你好"
ai_response = plugin.on_messages("用户名", [user_message])
# 返回: "你好！我是AI助手，很高兴为您服务！"
```

#### 聊天类型识别
- 自动从handler的chat_type_cache获取聊天类型
- 只处理chat_type为'friend'的私聊消息
- 群聊消息自动忽略，不会干扰群聊功能

### 2. 上下文感知回复

#### 历史记录利用
```python
# 系统自动获取最近20条对话记录
history = plugin._get_conversation_context(user_name, 20)

# 构建包含历史的提示词
prompt = plugin._build_context_prompt(history, current_messages, user_config)
```

#### 上下文提示词示例
```
你是AI助手，以friendly的风格与用户对话。
你的特点包括：helpful, conversational, context_aware。

=== 历史对话上下文 ===
用户: 我想学习Python
助手: Python是一门很好的编程语言，适合初学者。您想从哪个方面开始学习呢？
用户: 我想了解基础语法
助手: 好的，Python的基础语法包括变量、数据类型、控制结构等...

=== 当前对话 ===
用户: 能给我推荐一些学习资源吗？

请基于以上对话历史，给出自然、有帮助的回复：
```

### 3. 用户配置管理

#### 默认配置
```python
default_config = {
    'enabled': True,           # 是否启用AI助手
    'ai_agent_id': 4,         # 使用的AI智能体ID
    'context_limit': 20,      # 上下文条数限制
    'session_hours': 24,      # 会话时长（小时）
    'personality': {          # 个性化设置
        'name': 'AI助手',
        'style': 'friendly',
        'features': ['helpful', 'conversational', 'context_aware']
    }
}
```

#### 配置管理方法
```python
# 更新用户配置
plugin.update_user_config(
    user_name="张三",
    ai_agent_id=5,
    context_limit=30,
    personality=json.dumps({
        'name': '小助手',
        'style': 'professional',
        'features': ['precise', 'technical']
    })
)

# 禁用/启用用户
plugin.disable_user("张三")
plugin.enable_user("张三")
```

### 4. 会话统计

#### 统计数据
- **消息数量**：用户发送的消息总数
- **AI回复数量**：AI生成的回复总数
- **会话时间**：首次和最后消息时间
- **活跃天数**：用户活跃的天数统计

#### 查询统计
```python
# 获取用户最近7天的会话统计
stats = plugin.get_user_session_stats("张三", days=7)

# 示例输出
[
    {
        'user_name': '张三',
        'session_date': '2024-01-15',
        'message_count': 10,
        'ai_reply_count': 8,
        'first_message_time': '2024-01-15 09:00:00',
        'last_message_time': '2024-01-15 18:30:00'
    }
]
```

## 数据库表结构

### private_ai_config 表
```sql
CREATE TABLE private_ai_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    ai_agent_id BIGINT DEFAULT 4,
    context_limit INT DEFAULT 20,
    session_hours INT DEFAULT 24,
    personality TEXT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user (user_name)
);
```

### private_ai_sessions 表
```sql
CREATE TABLE private_ai_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(255) NOT NULL,
    session_date DATE NOT NULL,
    message_count INT DEFAULT 0,
    ai_reply_count INT DEFAULT 0,
    first_message_time DATETIME NULL,
    last_message_time DATETIME NULL,
    UNIQUE KEY uk_user_date (user_name, session_date)
);
```

## 使用示例

### 基本使用
```python
# 插件会自动处理私聊消息
# 用户发送: "你好，我想了解一下AI"
# 系统自动: 保存用户消息 → 获取历史上下文 → 生成AI回复 → 保存AI回复 → 返回回复
```

### 配置管理
```python
# 为特定用户配置专业风格的AI助手
plugin.update_user_config(
    user_name="技术专家",
    ai_agent_id=4,
    context_limit=30,
    personality=json.dumps({
        'name': '技术助手',
        'style': 'professional',
        'features': ['technical', 'precise', 'detailed']
    })
)
```

### 数据查询
```python
# 查看用户对话历史
history = plugin.get_user_conversation_history("张三", limit=10)

# 查看用户会话统计
stats = plugin.get_user_session_stats("张三", days=30)

# 清理旧数据
deleted_count = plugin.cleanup_old_data(days_to_keep=90)
```

## 部署配置

### 1. 确保智能体4存在
```sql
-- 检查智能体4是否存在
SELECT * FROM ai_agent_profiles WHERE id = 4;

-- 如果不存在，创建智能体4
INSERT INTO ai_agent_profiles (id, name, description, api_key, url, model_type)
VALUES (4, 'Default AI Assistant', '默认AI助手', 'your_api_key', 'your_api_url', 'gpt-3.5-turbo');
```

### 2. 插件优先级
```python
priority = 100  # 较高优先级，在关键词插件之前执行
```

### 3. 配置验证
```python
# 运行测试脚本验证功能
python plugins/private_ai_assistant/test_private_ai.py
```

## 监控和维护

### 日志监控
```
✅ 正常日志:
[私聊AI助手] 为用户 张三 创建默认配置
[私聊AI助手] 保存用户消息到历史记录 (ID: 123)
[私聊AI助手] 保存AI回复到历史记录 (回复ID: 124, 关联消息ID: 123)
[私聊AI助手] 为用户 张三 生成回复: 你好！我是AI助手...

❌ 错误日志:
[ERROR] [私聊AI助手] 未找到AI智能体配置 ID: 4
[ERROR] [私聊AI助手] 生成AI回复失败: API调用超时
```

### 性能监控
```sql
-- 查看用户活跃度
SELECT user_name, SUM(message_count) as total_messages, COUNT(*) as active_days
FROM private_ai_sessions 
WHERE session_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY user_name 
ORDER BY total_messages DESC;

-- 查看AI回复成功率
SELECT 
    SUM(message_count) as user_messages,
    SUM(ai_reply_count) as ai_replies,
    ROUND(SUM(ai_reply_count) / SUM(message_count) * 100, 2) as reply_rate
FROM private_ai_sessions;
```

### 定期维护
```python
# 每周清理90天前的数据
plugin.cleanup_old_data(days_to_keep=90)

# 优化数据库表
# OPTIMIZE TABLE private_ai_config, private_ai_sessions, conversation_history;
```

## 注意事项

1. **智能体4配置**：确保数据库中存在ID为4的AI智能体配置
2. **API配置**：确保智能体的API密钥和URL配置正确
3. **私聊识别**：依赖handler的chat_type_cache正确识别私聊
4. **性能考虑**：大量私聊用户可能增加数据库负载
5. **上下文长度**：合理设置context_limit避免提示词过长

## 扩展功能

### 自定义个性化
- 支持为不同用户设置不同的AI个性
- 可配置回复风格和特点
- 支持多语言个性化设置

### 智能推荐
- 基于用户历史偏好推荐内容
- 学习用户交互模式
- 提供个性化建议

### 数据分析
- 用户行为分析
- 对话质量评估
- 使用模式统计

私聊AI助手插件为用户提供了智能、个性化的私聊体验，通过上下文感知和历史记录利用，实现了真正的对话式AI交互。

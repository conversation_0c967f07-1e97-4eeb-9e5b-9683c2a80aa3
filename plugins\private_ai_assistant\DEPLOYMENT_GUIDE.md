# 私聊AI助手插件部署指南

## 快速部署

### 1. 前置条件检查

确保以下条件满足：
- ✅ MySQL数据库正常运行
- ✅ conversation_history插件已部署
- ✅ 数据库用户有CREATE、ALTER、INSERT、SELECT权限
- ✅ 微信机器人系统正常运行

### 2. 数据库配置

#### 步骤1：运行配置脚本
```bash
# 连接到MySQL数据库
mysql -u your_username -p your_database

# 执行配置脚本
source plugins/private_ai_assistant/setup_agent_4.sql
```

#### 步骤2：配置智能体4
```sql
-- 更新智能体4的真实API配置
UPDATE ai_agent_profiles 
SET 
    api_key = "sk-your-real-api-key",
    url = "https://your-api-endpoint.com/v1/chat/completions"
WHERE id = 4;
```

#### 步骤3：验证配置
```sql
-- 检查智能体4配置
SELECT id, name, model_type, 
       CASE WHEN api_key LIKE 'sk-%' THEN '✅ 已配置' ELSE '❌ 未配置' END as api_status
FROM ai_agent_profiles WHERE id = 4;

-- 检查表结构
SHOW TABLES LIKE 'private_ai_%';
```

### 3. 插件部署

#### 自动发现机制
插件会被自动发现和加载，无需手动注册：

```
plugins/
├── private_ai_assistant/
│   ├── __init__.py          # 插件入口
│   ├── plugin.py            # 主要逻辑
│   ├── sql.py              # 数据库函数
│   └── ...
```

#### 验证插件加载
启动微信机器人，查看日志输出：
```
✅ 发现插件: PrivateAIAssistantPlugin
🔧 插件已加载: PrivateAIAssistantPlugin
✅ [私聊AI助手] 数据库表初始化完成
✅ [私聊AI助手] 插件初始化完成
```

### 4. 功能测试

#### 基本功能测试
1. **发送私聊消息**：向机器人发送任意私聊消息
2. **检查回复**：确认收到AI回复
3. **查看日志**：确认处理流程正常

#### 测试脚本
```bash
# 修改数据库配置
vim plugins/private_ai_assistant/test_private_ai.py

# 运行测试
python plugins/private_ai_assistant/test_private_ai.py
```

#### 手动测试步骤
```python
# 1. 发送测试消息
# 用户: "你好"
# 预期: 收到AI回复

# 2. 发送上下文相关消息
# 用户: "我刚才问了什么？"
# 预期: AI能够回忆之前的对话

# 3. 检查群聊不受影响
# 在群聊中发送消息，确认私聊AI不会响应
```

## 配置说明

### 1. 智能体4配置

#### 支持的模型类型
```python
MODEL_TYPES = {
    "gpt-3.5-turbo": "OpenAI GPT-3.5",
    "dify_chatflow": "Dify ChatFlow",
    "dify_workflow": "Dify WorkFlow", 
    "dify_agent": "Dify Agent"
}
```

#### API配置示例
```sql
-- OpenAI API配置
UPDATE ai_agent_profiles SET 
    api_key = "sk-your-openai-key",
    url = "https://api.openai.com/v1/chat/completions",
    model_type = "gpt-3.5-turbo"
WHERE id = 4;

-- Dify API配置
UPDATE ai_agent_profiles SET 
    api_key = "app-your-dify-key",
    url = "https://api.dify.ai/v1/chat-messages",
    model_type = "dify_chatflow"
WHERE id = 4;
```

### 2. 用户个性化配置

#### 默认配置
```python
default_config = {
    'enabled': True,           # 启用AI助手
    'ai_agent_id': 4,         # 使用智能体4
    'context_limit': 20,      # 上下文条数
    'session_hours': 24,      # 会话时长
    'personality': {          # 个性化设置
        'name': 'AI助手',
        'style': 'friendly',
        'features': ['helpful', 'conversational', 'context_aware']
    }
}
```

#### 自定义用户配置
```sql
-- 为特定用户配置专业风格AI
INSERT INTO private_ai_config (
    user_name, ai_agent_id, context_limit, personality
) VALUES (
    '技术专家',
    4,
    30,
    '{"name": "技术助手", "style": "professional", "features": ["technical", "precise"]}'
);
```

### 3. 插件优先级

```python
priority = 100  # 较高优先级，在关键词插件之前执行
```

优先级说明：
- **100**: 私聊AI助手（本插件）
- **150**: dayan_Plugin_ai_chat
- **200**: conversation_history

## 监控和维护

### 1. 日志监控

#### 正常运行日志
```
[私聊AI助手] 插件初始化完成
[私聊AI助手] 为用户 张三 创建默认配置
[私聊AI助手] 保存用户消息到历史记录 (ID: 123)
[私聊AI助手] 保存AI回复到历史记录 (回复ID: 124, 关联消息ID: 123)
[私聊AI助手] 为用户 张三 生成回复: 你好！我是AI助手...
```

#### 错误日志
```
[ERROR] [私聊AI助手] 未找到AI智能体配置 ID: 4
[ERROR] [私聊AI助手] 生成AI回复失败: API调用超时
[WARNING] [私聊AI助手] 用户消息信息为空，无法保存AI回复关联
```

### 2. 性能监控

#### 数据库查询
```sql
-- 查看活跃用户
SELECT user_name, SUM(message_count) as total_messages
FROM private_ai_sessions 
WHERE session_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY user_name 
ORDER BY total_messages DESC 
LIMIT 10;

-- 查看AI回复成功率
SELECT 
    DATE(session_date) as date,
    SUM(message_count) as user_messages,
    SUM(ai_reply_count) as ai_replies,
    ROUND(SUM(ai_reply_count) / SUM(message_count) * 100, 2) as success_rate
FROM private_ai_sessions 
WHERE session_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(session_date)
ORDER BY date DESC;
```

#### 系统资源监控
- 监控数据库连接数
- 监控API调用频率
- 监控响应时间

### 3. 定期维护

#### 数据清理
```python
# 每周执行一次数据清理
plugin.cleanup_old_data(days_to_keep=90)
```

#### 数据库优化
```sql
-- 每月执行一次表优化
OPTIMIZE TABLE private_ai_config;
OPTIMIZE TABLE private_ai_sessions;
OPTIMIZE TABLE conversation_history;

-- 分析表统计信息
ANALYZE TABLE private_ai_config;
ANALYZE TABLE private_ai_sessions;
```

## 故障排除

### 常见问题

#### 1. 插件未加载
```
错误: 未发现PrivateAIAssistantPlugin
解决: 检查插件目录结构和__init__.py文件
```

#### 2. 智能体4不存在
```
错误: 未找到AI智能体配置 ID: 4
解决: 运行setup_agent_4.sql脚本创建智能体4
```

#### 3. API调用失败
```
错误: 生成AI回复失败: API调用超时
解决: 检查API密钥、URL配置和网络连接
```

#### 4. 私聊识别错误
```
错误: 群聊消息被私聊AI处理
解决: 检查chat_type_cache的设置和聊天类型识别逻辑
```

### 调试步骤

1. **检查插件加载**
   - 查看启动日志
   - 确认插件在插件列表中

2. **检查数据库配置**
   - 验证智能体4存在
   - 检查API配置正确性

3. **检查聊天类型识别**
   - 确认chat_type_cache正确设置
   - 验证私聊/群聊识别逻辑

4. **检查API连通性**
   - 测试API端点可访问性
   - 验证API密钥有效性

## 高级配置

### 1. 多智能体支持

```sql
-- 为不同用户配置不同智能体
UPDATE private_ai_config 
SET ai_agent_id = 5 
WHERE user_name = '技术专家';

UPDATE private_ai_config 
SET ai_agent_id = 6 
WHERE user_name = '客服专员';
```

### 2. 个性化配置

```python
# 配置不同风格的AI助手
personalities = {
    'friendly': {
        'name': 'AI助手',
        'style': 'friendly',
        'features': ['helpful', 'conversational', 'empathetic']
    },
    'professional': {
        'name': '专业助手',
        'style': 'professional',
        'features': ['precise', 'technical', 'formal']
    },
    'casual': {
        'name': '小助手',
        'style': 'casual',
        'features': ['relaxed', 'humorous', 'creative']
    }
}
```

### 3. 上下文优化

```python
# 根据用户类型调整上下文长度
context_limits = {
    'power_user': 50,      # 高级用户，更多上下文
    'normal_user': 20,     # 普通用户，标准上下文
    'new_user': 10         # 新用户，较少上下文
}
```

部署完成后，私聊AI助手插件将为所有私聊用户提供智能、个性化的AI对话服务，利用上下文历史记录提供更好的用户体验。

-- 更新 conversation_history 表结构
-- 添加增强对话历史记录存储所需的新字段

-- 1. 添加对话ID字段，用于关联用户消息和AI回复
ALTER TABLE conversation_history 
ADD COLUMN conversation_id VARCHAR(64) NULL COMMENT '对话ID，用于关联用户消息和AI回复' 
AFTER session_id;

-- 2. 添加回复关联字段，用于建立回复关系
ALTER TABLE conversation_history 
ADD COLUMN reply_to_id BIGINT NULL COMMENT '回复的消息ID，用于建立回复关系' 
AFTER conversation_id;

-- 3. 添加消息批次ID字段，用于标识同一批次的消息
ALTER TABLE conversation_history 
ADD COLUMN message_batch_id VARCHAR(64) NULL COMMENT '消息批次ID，用于标识同一批次的消息' 
AFTER reply_to_id;

-- 4. 添加批次开始标识字段
ALTER TABLE conversation_history 
ADD COLUMN is_batch_start BOOLEAN DEFAULT FALSE COMMENT '是否为批次开始' 
AFTER message_batch_id;

-- 5. 添加批次结束标识字段
ALTER TABLE conversation_history 
ADD COLUMN is_batch_end BOOLEAN DEFAULT FALSE COMMENT '是否为批次结束' 
AFTER is_batch_start;

-- 6. 添加索引以提升查询性能
CREATE INDEX idx_conversation_id ON conversation_history(conversation_id);
CREATE INDEX idx_reply_to_id ON conversation_history(reply_to_id);
CREATE INDEX idx_message_batch_id ON conversation_history(message_batch_id);

-- 7. 添加外键约束（可选，如果需要严格的数据完整性）
-- ALTER TABLE conversation_history 
-- ADD CONSTRAINT fk_reply_to_id 
-- FOREIGN KEY (reply_to_id) REFERENCES conversation_history(id) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 8. 验证表结构更新
DESCRIBE conversation_history;

-- 9. 查看索引信息
SHOW INDEX FROM conversation_history;

-- 执行完成后，您的表结构应该包含以下字段：
-- id, chat_name, chat_type, sender, message_content, message_type, session_id, 
-- conversation_id, reply_to_id, message_batch_id, is_batch_start, is_batch_end, created_at

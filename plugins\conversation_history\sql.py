"""
Conversation History Plugin SQL Functions
包含对话记录插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict, Tuple
from datetime import datetime, timedelta


def init_conversation_tables(self):
    """初始化对话记录相关表"""
    cursor = self.connection.cursor()

    # 创建对话记录表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_history (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            sender VARCHAR(255) NOT NULL COMMENT '发送者',
            message_content TEXT NOT NULL COMMENT '消息内容',
            message_type ENUM('user', 'bot') NOT NULL DEFAULT 'user' COMMENT '消息类型',
            session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            INDEX idx_chat_session (chat_name, session_id),
            INDEX idx_created_at (created_at),
            INDEX idx_chat_type (chat_type),
            INDEX idx_sender (sender)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建会话配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用对话记录',
            context_limit INT DEFAULT 100 COMMENT '上下文条数限制',
            session_hours INT DEFAULT 24 COMMENT '会话时长（小时）',
            ai_agent_id BIGINT NULL COMMENT '关联的AI智能体ID',
            trigger_keywords TEXT NULL COMMENT '触发关键词，JSON格式',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uk_chat (chat_name, chat_type),
            INDEX idx_enabled (enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def generate_session_id(self, chat_name: str, chat_type: str, session_hours: int = 24) -> str:
    """生成会话ID，基于聊天名称和时间窗口"""
    import hashlib
    from datetime import datetime

    # 计算时间窗口
    now = datetime.now()
    time_window = now.replace(minute=0, second=0, microsecond=0)
    time_window = time_window - timedelta(hours=time_window.hour % session_hours)

    # 生成会话ID
    session_key = f"{chat_name}_{chat_type}_{time_window.strftime('%Y%m%d_%H')}"
    session_id = hashlib.md5(session_key.encode()).hexdigest()[:16]

    return session_id


def save_message(self, chat_name: str, chat_type: str, sender: str,
                message_content: str, message_type: str = 'user', session_hours: int = 24):
    """保存消息到对话记录"""
    cursor = self.connection.cursor()

    # 生成会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        INSERT INTO conversation_history
        (chat_name, chat_type, sender, message_content, message_type, session_id)
        VALUES (%s, %s, %s, %s, %s, %s)
    """, (chat_name, chat_type, sender, message_content, message_type, session_id))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_conversation_context(self, chat_name: str, chat_type: str,
                           limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取对话上下文"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_user_conversation_context(self, chat_name: str, chat_type: str, sender: str,
                                limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取特定用户的对话上下文（包括该用户的消息和AI回复）"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        AND (sender = %s OR message_type = 'bot')
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, sender, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_conversation_config(self, chat_name: str, chat_type: str) -> Optional[Dict]:
    """获取对话配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM conversation_config
        WHERE chat_name = %s AND chat_type = %s
    """, (chat_name, chat_type))

    return cursor.fetchone()


def upsert_conversation_config(self, chat_name: str, chat_type: str,
                              enabled: bool = True, context_limit: int = 100,
                              session_hours: int = 24, ai_agent_id: int = None,
                              trigger_keywords: str = None):
    """插入或更新对话配置"""
    cursor = self.connection.cursor()

    cursor.execute("""
        INSERT INTO conversation_config
        (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            enabled = VALUES(enabled),
            context_limit = VALUES(context_limit),
            session_hours = VALUES(session_hours),
            ai_agent_id = VALUES(ai_agent_id),
            trigger_keywords = VALUES(trigger_keywords),
            updated_at = CURRENT_TIMESTAMP
    """, (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_ai_agent_by_id(self, agent_id: int) -> Optional[Dict]:
    """根据ID获取AI智能体配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        WHERE id = %s
    """, (agent_id,))

    return cursor.fetchone()


def cleanup_old_conversations(self, days_to_keep: int = 30):
    """清理旧的对话记录"""
    cursor = self.connection.cursor()

    cutoff_date = datetime.now() - timedelta(days=days_to_keep)

    cursor.execute("""
        DELETE FROM conversation_history
        WHERE created_at < %s
    """, (cutoff_date,))

    deleted_count = cursor.rowcount

    try:
        self.connection.commit()
        return deleted_count
    except Exception as e:
        self.connection.rollback()
        raise e


# 导出SQL函数字典
CONVERSATION_HISTORY_SQL_FUNCTIONS = {
    'init_conversation_tables': init_conversation_tables,
    'generate_session_id': generate_session_id,
    'save_message': save_message,
    'get_conversation_context': get_conversation_context,
    'get_user_conversation_context': get_user_conversation_context,
    'get_conversation_config': get_conversation_config,
    'upsert_conversation_config': upsert_conversation_config,
    'get_ai_agent_by_id': get_ai_agent_by_id,
    'cleanup_old_conversations': cleanup_old_conversations,
}

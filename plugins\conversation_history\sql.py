"""
Conversation History Plugin SQL Functions
包含对话记录插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict, Tuple
from datetime import datetime, timedelta


def init_conversation_tables(self):
    """初始化对话记录相关表"""
    cursor = self.connection.cursor()

    # 创建对话记录表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_history (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            sender VARCHAR(255) NOT NULL COMMENT '发送者',
            message_content TEXT NOT NULL COMMENT '消息内容',
            message_type ENUM('user', 'bot') NOT NULL DEFAULT 'user' COMMENT '消息类型',
            session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
            conversation_id VARCHAR(64) NULL COMMENT '对话ID，用于关联用户消息和AI回复',
            reply_to_id BIGINT NULL COMMENT '回复的消息ID，用于建立回复关系',
            message_batch_id VARCHAR(64) NULL COMMENT '消息批次ID，用于标识同一批次的消息',
            is_batch_start BOOLEAN DEFAULT FALSE COMMENT '是否为批次开始',
            is_batch_end BOOLEAN DEFAULT FALSE COMMENT '是否为批次结束',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            INDEX idx_chat_session (chat_name, session_id),
            INDEX idx_created_at (created_at),
            INDEX idx_chat_type (chat_type),
            INDEX idx_sender (sender),
            INDEX idx_conversation_id (conversation_id),
            INDEX idx_reply_to_id (reply_to_id),
            INDEX idx_message_batch_id (message_batch_id),
            FOREIGN KEY (reply_to_id) REFERENCES conversation_history(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建会话配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用对话记录',
            context_limit INT DEFAULT 100 COMMENT '上下文条数限制',
            session_hours INT DEFAULT 24 COMMENT '会话时长（小时）',
            ai_agent_id BIGINT NULL COMMENT '关联的AI智能体ID',
            trigger_keywords TEXT NULL COMMENT '触发关键词，JSON格式',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uk_chat (chat_name, chat_type),
            INDEX idx_enabled (enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def generate_session_id(self, chat_name: str, chat_type: str, session_hours: int = 24) -> str:
    """生成会话ID，基于聊天名称和时间窗口"""
    import hashlib
    from datetime import datetime

    # 计算时间窗口
    now = datetime.now()
    time_window = now.replace(minute=0, second=0, microsecond=0)
    time_window = time_window - timedelta(hours=time_window.hour % session_hours)

    # 生成会话ID
    session_key = f"{chat_name}_{chat_type}_{time_window.strftime('%Y%m%d_%H')}"
    session_id = hashlib.md5(session_key.encode()).hexdigest()[:16]

    return session_id


def save_message(self, chat_name: str, chat_type: str, sender: str,
                message_content: str, message_type: str = 'user', session_hours: int = 24,
                conversation_id: str = None, reply_to_id: int = None,
                message_batch_id: str = None, is_batch_start: bool = False,
                is_batch_end: bool = False) -> int:
    """保存消息到对话记录，返回插入的消息ID"""
    cursor = self.connection.cursor()

    # 生成会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        INSERT INTO conversation_history
        (chat_name, chat_type, sender, message_content, message_type, session_id,
         conversation_id, reply_to_id, message_batch_id, is_batch_start, is_batch_end)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (chat_name, chat_type, sender, message_content, message_type, session_id,
          conversation_id, reply_to_id, message_batch_id, is_batch_start, is_batch_end))

    message_id = cursor.lastrowid

    try:
        self.connection.commit()
        return message_id
    except Exception as e:
        self.connection.rollback()
        raise e


def get_conversation_context(self, chat_name: str, chat_type: str,
                           limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取对话上下文"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_user_conversation_context(self, chat_name: str, chat_type: str, sender: str,
                                limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取特定用户的对话上下文（包括该用户的消息和AI回复）"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        AND (sender = %s OR message_type = 'bot')
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, sender, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_conversation_config(self, chat_name: str, chat_type: str) -> Optional[Dict]:
    """获取对话配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM conversation_config
        WHERE chat_name = %s AND chat_type = %s
    """, (chat_name, chat_type))

    return cursor.fetchone()


def upsert_conversation_config(self, chat_name: str, chat_type: str,
                              enabled: bool = True, context_limit: int = 100,
                              session_hours: int = 24, ai_agent_id: int = None,
                              trigger_keywords: str = None):
    """插入或更新对话配置"""
    cursor = self.connection.cursor()

    cursor.execute("""
        INSERT INTO conversation_config
        (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            enabled = VALUES(enabled),
            context_limit = VALUES(context_limit),
            session_hours = VALUES(session_hours),
            ai_agent_id = VALUES(ai_agent_id),
            trigger_keywords = VALUES(trigger_keywords),
            updated_at = CURRENT_TIMESTAMP
    """, (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_ai_agent_by_id(self, agent_id: int) -> Optional[Dict]:
    """根据ID获取AI智能体配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        WHERE id = %s
    """, (agent_id,))

    return cursor.fetchone()


def cleanup_old_conversations(self, days_to_keep: int = 30):
    """清理旧的对话记录"""
    cursor = self.connection.cursor()

    cutoff_date = datetime.now() - timedelta(days=days_to_keep)

    cursor.execute("""
        DELETE FROM conversation_history
        WHERE created_at < %s
    """, (cutoff_date,))

    deleted_count = cursor.rowcount

    try:
        self.connection.commit()
        return deleted_count
    except Exception as e:
        self.connection.rollback()
        raise e


def generate_conversation_id(self) -> str:
    """生成对话ID"""
    import uuid
    return str(uuid.uuid4())[:16]


def generate_batch_id(self) -> str:
    """生成批次ID"""
    import uuid
    return str(uuid.uuid4())[:16]


def save_user_message_with_batch(self, chat_name: str, chat_type: str, sender: str,
                                message_content: str, session_hours: int = 24,
                                conversation_id: str = None, message_batch_id: str = None,
                                is_batch_start: bool = False, is_batch_end: bool = False) -> int:
    """保存用户消息并支持批次处理"""
    return self.save_message(
        chat_name=chat_name,
        chat_type=chat_type,
        sender=sender,
        message_content=message_content,
        message_type='user',
        session_hours=session_hours,
        conversation_id=conversation_id,
        message_batch_id=message_batch_id,
        is_batch_start=is_batch_start,
        is_batch_end=is_batch_end
    )


def save_bot_reply(self, chat_name: str, chat_type: str, message_content: str,
                  session_hours: int = 24, conversation_id: str = None,
                  reply_to_id: int = None, message_batch_id: str = None) -> int:
    """保存AI回复消息"""
    return self.save_message(
        chat_name=chat_name,
        chat_type=chat_type,
        sender="AI助手",
        message_content=message_content,
        message_type='bot',
        session_hours=session_hours,
        conversation_id=conversation_id,
        reply_to_id=reply_to_id,
        message_batch_id=message_batch_id
    )


def get_conversation_by_id(self, conversation_id: str) -> List[Dict]:
    """根据对话ID获取完整对话"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT id, sender, message_content, message_type, reply_to_id,
               is_batch_start, is_batch_end, created_at
        FROM conversation_history
        WHERE conversation_id = %s
        ORDER BY created_at ASC
    """, (conversation_id,))

    return cursor.fetchall()


def get_message_with_replies(self, message_id: int) -> Dict:
    """获取消息及其所有回复"""
    cursor = self.connection.cursor(dictionary=True)

    # 获取原始消息
    cursor.execute("""
        SELECT id, sender, message_content, message_type, conversation_id,
               message_batch_id, is_batch_start, is_batch_end, created_at
        FROM conversation_history
        WHERE id = %s
    """, (message_id,))

    original_message = cursor.fetchone()
    if not original_message:
        return None

    # 获取所有回复
    cursor.execute("""
        SELECT id, sender, message_content, message_type, conversation_id,
               reply_to_id, message_batch_id, created_at
        FROM conversation_history
        WHERE reply_to_id = %s
        ORDER BY created_at ASC
    """, (message_id,))

    replies = cursor.fetchall()

    return {
        'original_message': original_message,
        'replies': replies
    }


# 导出SQL函数字典
CONVERSATION_HISTORY_SQL_FUNCTIONS = {
    'init_conversation_tables': init_conversation_tables,
    'generate_session_id': generate_session_id,
    'save_message': save_message,
    'get_conversation_context': get_conversation_context,
    'get_user_conversation_context': get_user_conversation_context,
    'get_conversation_config': get_conversation_config,
    'upsert_conversation_config': upsert_conversation_config,
    'get_ai_agent_by_id': get_ai_agent_by_id,
    'cleanup_old_conversations': cleanup_old_conversations,
    'generate_conversation_id': generate_conversation_id,
    'generate_batch_id': generate_batch_id,
    'save_user_message_with_batch': save_user_message_with_batch,
    'save_bot_reply': save_bot_reply,
    'get_conversation_by_id': get_conversation_by_id,
    'get_message_with_replies': get_message_with_replies,
}

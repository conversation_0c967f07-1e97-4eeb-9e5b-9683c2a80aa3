# dayan_Plugin_ai_chat 历史记录功能部署指南

## 快速部署

### 1. 前置条件检查

确保以下条件满足：
- ✅ MySQL数据库正常运行
- ✅ 数据库用户有CREATE、ALTER、INSERT、SELECT权限
- ✅ conversation_history插件已存在
- ✅ AI智能体配置正确

### 2. 数据库迁移

#### 方法一：使用迁移脚本（推荐）
```python
from plugins.conversation_history.migrate_enhanced_fields import migrate_conversation_history_table

# 执行数据库迁移
success = migrate_conversation_history_table(connection)
if success:
    print("✅ 数据库迁移完成")
else:
    print("❌ 数据库迁移失败")
```

#### 方法二：手动执行SQL
```sql
-- 添加新字段
ALTER TABLE conversation_history 
ADD COLUMN conversation_id VARCHAR(64) NULL COMMENT '对话ID，用于关联用户消息和AI回复';

ALTER TABLE conversation_history 
ADD COLUMN reply_to_id BIGINT NULL COMMENT '回复的消息ID，用于建立回复关系';

ALTER TABLE conversation_history 
ADD COLUMN message_batch_id VARCHAR(64) NULL COMMENT '消息批次ID，用于标识同一批次的消息';

ALTER TABLE conversation_history 
ADD COLUMN is_batch_start BOOLEAN DEFAULT FALSE COMMENT '是否为批次开始';

ALTER TABLE conversation_history 
ADD COLUMN is_batch_end BOOLEAN DEFAULT FALSE COMMENT '是否为批次结束';

-- 添加索引
ALTER TABLE conversation_history ADD INDEX idx_conversation_id (conversation_id);
ALTER TABLE conversation_history ADD INDEX idx_reply_to_id (reply_to_id);
ALTER TABLE conversation_history ADD INDEX idx_message_batch_id (message_batch_id);
```

### 3. 插件部署

#### 替换插件文件
将优化后的插件文件复制到对应位置：
```bash
# 备份原文件
cp plugins/dayan_Plugin_ai_chat/plugin.py plugins/dayan_Plugin_ai_chat/plugin.py.backup

# 部署新文件（已包含历史记录功能）
# 新的plugin.py文件已经包含所有优化
```

#### 验证插件加载
```python
# 检查插件是否正确加载
from plugins.dayan_Plugin_ai_chat.plugin import AIChatPlugin

# 插件应该能正常初始化
plugin = AIChatPlugin(handler)
print("✅ 插件加载成功")
```

### 4. 功能测试

#### 运行测试脚本
```bash
# 修改数据库配置
vim plugins/dayan_Plugin_ai_chat/test_history_storage.py

# 运行测试
python plugins/dayan_Plugin_ai_chat/test_history_storage.py
```

#### 手动测试
```python
# 发送测试消息
messages = ["AI 你好"]
response = plugin.on_messages("测试群", messages)

# 检查历史记录
history = plugin.get_conversation_history("测试群", "group", 5)
print(f"历史记录数量: {len(history)}")
```

## 配置说明

### 1. 数据库配置

确保数据库连接配置正确：
```python
db_config = {
    'host': 'localhost',
    'user': 'your_username',
    'password': 'your_password',
    'database': 'wechat_bot',
    'charset': 'utf8mb4'
}
```

### 2. AI智能体配置

确保AI智能体配置表中有有效数据：
```sql
-- 检查AI智能体配置
SELECT * FROM ai_agent_profiles;

-- 检查关键词规则
SELECT * FROM ai_keyword_rules WHERE enabled = TRUE;
```

### 3. 关键词规则配置

确保关键词规则正确设置：
```sql
-- 示例：添加全局AI关键词规则
INSERT INTO ai_keyword_rules (chat_name, chat_type, global_rule, enabled) 
VALUES ('*', 'group', TRUE, TRUE);

INSERT INTO ai_keyword_rules_collection (keyword_id, user_keyword, agent_id, reply_prompt)
VALUES (LAST_INSERT_ID(), 'AI', 1, '你是一个智能助手，请回答用户的问题。');
```

## 验证部署

### 1. 数据库验证
```sql
-- 检查表结构
DESCRIBE conversation_history;

-- 检查索引
SHOW INDEX FROM conversation_history;

-- 检查数据
SELECT COUNT(*) FROM conversation_history;
```

### 2. 功能验证
```python
# 测试基本功能
def test_basic_functionality():
    # 1. 测试消息处理
    response = plugin.on_messages("测试群", ["AI 测试"])
    assert response is not None
    
    # 2. 测试历史查询
    history = plugin.get_conversation_history("测试群", "group", 1)
    assert len(history) > 0
    
    # 3. 测试关联关系
    user_msg = next((h for h in history if h['message_type'] == 'user'), None)
    bot_msg = next((h for h in history if h['message_type'] == 'bot'), None)
    
    if user_msg and bot_msg:
        assert bot_msg['reply_to_id'] == user_msg['id']
    
    print("✅ 基本功能验证通过")
```

### 3. 性能验证
```python
import time

def test_performance():
    start_time = time.time()
    
    # 测试批量消息处理
    for i in range(10):
        plugin.on_messages(f"测试群{i}", [f"AI 测试消息{i}"])
    
    end_time = time.time()
    print(f"处理10条消息耗时: {end_time - start_time:.2f}秒")
```

## 监控和维护

### 1. 日志监控

关注以下日志信息：
```
✅ 正常日志:
[AIChatPlugin] 保存用户消息到历史记录 (ID: 123)
[AIChatPlugin] 保存AI回复到历史记录 (回复ID: 124, 关联消息ID: 123, 智能体ID: 1)

❌ 错误日志:
[ERROR] [AIChatPlugin] 保存用户消息失败: 数据库连接错误
[WARNING] [AIChatPlugin] 用户消息信息为空，无法保存AI回复关联
```

### 2. 数据库监控

定期检查：
```sql
-- 检查数据增长
SELECT DATE(created_at) as date, COUNT(*) as count 
FROM conversation_history 
GROUP BY DATE(created_at) 
ORDER BY date DESC 
LIMIT 7;

-- 检查关联关系完整性
SELECT 
    SUM(CASE WHEN message_type = 'user' THEN 1 ELSE 0 END) as user_messages,
    SUM(CASE WHEN message_type = 'bot' THEN 1 ELSE 0 END) as bot_messages,
    SUM(CASE WHEN message_type = 'bot' AND reply_to_id IS NOT NULL THEN 1 ELSE 0 END) as linked_bot_messages
FROM conversation_history;
```

### 3. 定期维护

#### 清理旧数据
```python
# 每周清理30天前的数据
deleted_count = plugin.cleanup_old_conversations(days_to_keep=30)
print(f"清理了 {deleted_count} 条旧记录")
```

#### 优化数据库
```sql
-- 定期优化表
OPTIMIZE TABLE conversation_history;

-- 分析表统计信息
ANALYZE TABLE conversation_history;
```

## 故障排除

### 常见问题

#### 1. 插件初始化失败
```
错误: ❌ handler 未绑定数据库实例，无法继续
解决: 确保handler正确绑定了数据库实例
```

#### 2. 数据库表不存在
```
错误: Table 'conversation_history' doesn't exist
解决: 运行数据库迁移脚本或手动创建表
```

#### 3. AI回复未存储
```
错误: AI回复生成但未保存到数据库
解决: 检查_save_ai_reply_to_history方法的调用和错误日志
```

#### 4. 关联关系错误
```
错误: reply_to_id为NULL或指向错误的消息
解决: 检查用户消息保存是否成功，验证返回的message_id
```

### 调试步骤

1. **检查日志输出**
2. **运行测试脚本**
3. **查询数据库验证数据**
4. **检查插件配置**
5. **验证AI智能体配置**

## 回滚方案

如果需要回滚到原版本：

1. **恢复插件文件**
```bash
cp plugins/dayan_Plugin_ai_chat/plugin.py.backup plugins/dayan_Plugin_ai_chat/plugin.py
```

2. **保留数据库结构**（推荐）
```sql
-- 新字段允许NULL，不影响原功能
-- 建议保留新字段以便将来使用
```

3. **完全回滚数据库**（不推荐）
```sql
-- 仅在必要时执行
ALTER TABLE conversation_history DROP COLUMN conversation_id;
ALTER TABLE conversation_history DROP COLUMN reply_to_id;
ALTER TABLE conversation_history DROP COLUMN message_batch_id;
ALTER TABLE conversation_history DROP COLUMN is_batch_start;
ALTER TABLE conversation_history DROP COLUMN is_batch_end;
```

## 技术支持

如遇到问题，请：
1. 查看详细错误日志
2. 运行测试脚本诊断
3. 检查数据库状态
4. 参考故障排除指南

部署完成后，dayan_Plugin_ai_chat插件将具备完整的历史记录存储功能，AI回复将被正确保存并与用户消息建立关联关系。

"""
测试优化后的conversation_history插件群聊功能
验证自动配置创建、关键词触发和AI回复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import mysql.connector
from typing import Dict, List
import json
from unittest.mock import Mock


class TestEnhancedGroupChat:
    def __init__(self, db_config: Dict):
        """初始化测试类"""
        self.connection = mysql.connector.connect(**db_config)
        
        # 模拟handler
        self.mock_handler = Mock()
        self.mock_handler.db = self._create_mock_db()
        self.mock_handler._log = self._mock_log
        self.mock_handler.chat_type_cache = {}  # 聊天类型缓存
        
        # 导入并初始化插件
        from plugins.conversation_history.plugin import ConversationHistoryPlugin
        self.plugin = ConversationHistoryPlugin(self.mock_handler)
    
    def _create_mock_db(self):
        """创建模拟数据库实例"""
        from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS
        
        mock_db = Mock()
        mock_db.connection = self.connection
        
        # 绑定SQL函数
        for func_name, func in CONVERSATION_HISTORY_SQL_FUNCTIONS.items():
            setattr(mock_db, func_name, func.__get__(mock_db, mock_db.__class__))
        
        # 模拟注册和卸载函数
        mock_db.register_plugin_functions = Mock()
        mock_db.unregister_plugin_functions = Mock()
        
        return mock_db
    
    def _mock_log(self, message, level="INFO"):
        """模拟日志输出"""
        print(f"[{level}] {message}")
    
    def setup_test_data(self):
        """设置测试数据"""
        print("=== 设置测试数据 ===")
        
        # 初始化表结构
        self.plugin.db.init_conversation_tables()
        print("✓ 对话记录表初始化完成")
        
        # 清理测试数据
        cursor = self.connection.cursor()
        cursor.execute("DELETE FROM conversation_config WHERE chat_name LIKE 'test_%'")
        cursor.execute("DELETE FROM conversation_history WHERE chat_name LIKE 'test_%'")
        cursor.execute("DELETE FROM ai_agent_profiles WHERE name LIKE 'test_%'")
        self.connection.commit()
        cursor.close()
        print("✓ 清理旧测试数据")
        
        # 创建测试智能体4
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO ai_agent_profiles (id, name, description, api_key, url, model_type)
            VALUES (4, 'test_agent_4', '测试智能体4', 'test_key', 'http://test.com', 'gpt-3.5-turbo')
            ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                description = VALUES(description),
                api_key = VALUES(api_key),
                url = VALUES(url),
                model_type = VALUES(model_type)
        """)
        self.connection.commit()
        cursor.close()
        print("✓ 创建/更新测试智能体4")
        
        # 设置聊天类型缓存
        self.mock_handler.chat_type_cache = {
            'test_group_1': 'group',
            'test_group_2': 'group',
            'test_private_1': 'friend'
        }
        print("✓ 设置聊天类型缓存")
    
    def test_auto_config_creation(self):
        """测试自动配置创建功能"""
        print("\n=== 测试自动配置创建功能 ===")
        
        chat_name = "test_group_new"
        chat_type = "group"
        
        # 确保配置不存在
        cursor = self.connection.cursor()
        cursor.execute("DELETE FROM conversation_config WHERE chat_name = %s", (chat_name,))
        self.connection.commit()
        cursor.close()
        
        # 获取配置（应该自动创建）
        config = self.plugin._get_chat_config(chat_name, chat_type)
        
        print(f"自动创建的配置: {config}")
        
        # 验证配置内容
        expected_fields = ['chat_name', 'chat_type', 'enabled', 'ai_agent_id', 'trigger_keywords']
        missing_fields = [field for field in expected_fields if field not in config]
        
        if not missing_fields and config.get('ai_agent_id') == 4:
            print("✓ 自动配置创建功能正常")
            return True
        else:
            print(f"✗ 自动配置创建功能异常，缺少字段: {missing_fields}")
            return False
    
    def test_keyword_trigger(self):
        """测试关键词触发功能"""
        print("\n=== 测试关键词触发功能 ===")
        
        # 测试触发关键词
        trigger_keywords = ["AI", "ai", "助手", "机器人"]
        
        test_cases = [
            (["你好AI"], True, "包含AI关键词"),
            (["请问助手"], True, "包含助手关键词"),
            (["机器人你好"], True, "包含机器人关键词"),
            (["普通聊天内容"], False, "不包含关键词"),
            (["ai能帮我吗"], True, "包含小写ai"),
            (["今天天气不错"], False, "普通对话")
        ]
        
        success_count = 0
        for messages, expected, description in test_cases:
            result = self.plugin._should_trigger_ai(messages, trigger_keywords)
            if result == expected:
                print(f"✓ {description}: {result}")
                success_count += 1
            else:
                print(f"✗ {description}: 期望{expected}，实际{result}")
        
        if success_count == len(test_cases):
            print("✓ 关键词触发功能正常")
            return True
        else:
            print(f"✗ 关键词触发功能异常: {success_count}/{len(test_cases)} 通过")
            return False
    
    def test_group_message_processing(self):
        """测试群聊消息处理功能"""
        print("\n=== 测试群聊消息处理功能 ===")
        
        chat_name = "test_group_1"
        
        # 测试不触发关键词的消息
        non_trigger_response = self.plugin.on_messages(chat_name, ["今天天气不错"])
        print(f"非触发消息回复: {non_trigger_response}")
        
        # 测试触发关键词的消息（会因为没有真实API而失败，但可以测试流程）
        try:
            trigger_response = self.plugin.on_messages(chat_name, ["AI你好，请介绍一下自己"])
            print(f"触发消息回复: {trigger_response}")
        except Exception as e:
            print(f"触发消息处理过程中的预期错误: {e}")
        
        # 检查消息是否被保存
        history = self.plugin.db.get_conversation_context(chat_name, 'group', 10)
        print(f"保存的历史记录: {len(history)} 条")
        
        if len(history) > 0:
            print("✓ 群聊消息处理和存储功能正常")
            return True
        else:
            print("✗ 群聊消息处理功能异常")
            return False
    
    def test_private_chat_filtering(self):
        """测试私聊过滤功能"""
        print("\n=== 测试私聊过滤功能 ===")
        
        # 设置私聊类型
        self.mock_handler.chat_type_cache['test_private_1'] = 'friend'
        
        # 私聊消息应该被过滤（返回None）
        private_response = self.plugin.on_messages('test_private_1', ['AI你好'])
        
        print(f"私聊消息回复: {private_response}")
        
        if private_response is None:
            print("✓ 私聊过滤功能正常")
            return True
        else:
            print("✗ 私聊过滤功能异常")
            return False
    
    def test_config_persistence(self):
        """测试配置持久化"""
        print("\n=== 测试配置持久化 ===")
        
        chat_name = "test_group_persist"
        chat_type = "group"
        
        # 第一次获取配置（自动创建）
        config1 = self.plugin._get_chat_config(chat_name, chat_type)
        
        # 清除缓存
        cache_key = f"{chat_name}_{chat_type}"
        if cache_key in self.plugin.config_cache:
            del self.plugin.config_cache[cache_key]
        
        # 第二次获取配置（从数据库读取）
        config2 = self.plugin._get_chat_config(chat_name, chat_type)
        
        print(f"第一次配置: {config1.get('ai_agent_id')}")
        print(f"第二次配置: {config2.get('ai_agent_id')}")
        
        if config1.get('ai_agent_id') == config2.get('ai_agent_id') == 4:
            print("✓ 配置持久化功能正常")
            return True
        else:
            print("✗ 配置持久化功能异常")
            return False
    
    def test_message_association(self):
        """测试消息关联功能"""
        print("\n=== 测试消息关联功能 ===")
        
        chat_name = "test_group_association"
        messages = ["AI测试消息关联功能"]
        
        # 手动保存用户消息
        config = self.plugin._get_chat_config(chat_name, 'group')
        user_info = self.plugin._save_user_messages(chat_name, 'group', messages, config)
        
        print(f"用户消息信息: {user_info}")
        
        # 验证消息关联信息
        if (user_info.get('message_id') and 
            user_info.get('conversation_id') and
            isinstance(user_info.get('message_id'), int)):
            print("✓ 消息关联功能正常")
            return True
        else:
            print("✗ 消息关联功能异常")
            return False
    
    def test_context_building(self):
        """测试上下文构建功能"""
        print("\n=== 测试上下文构建功能 ===")
        
        # 创建一些历史消息
        chat_name = "test_group_context"
        
        # 手动添加历史记录
        conversation_id = self.plugin.db.generate_conversation_id()
        
        # 添加用户消息
        user_msg_id = self.plugin.db.save_user_message_with_batch(
            chat_name=chat_name,
            chat_type='group',
            sender="测试用户",
            message_content="之前的问题",
            conversation_id=conversation_id
        )
        
        # 添加AI回复
        self.plugin.db.save_bot_reply(
            chat_name=chat_name,
            chat_type='group',
            message_content="之前的回复",
            conversation_id=conversation_id,
            reply_to_id=user_msg_id
        )
        
        # 获取上下文
        context = self.plugin.db.get_conversation_context(chat_name, 'group', 10)
        
        # 构建提示词
        current_messages = ["AI现在的问题"]
        prompt = self.plugin._build_context_prompt(context, current_messages)
        
        print("构建的提示词:")
        print(prompt[:200] + "..." if len(prompt) > 200 else prompt)
        
        if "历史对话上下文" in prompt and "之前的问题" in prompt:
            print("✓ 上下文构建功能正常")
            return True
        else:
            print("✗ 上下文构建功能异常")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试优化后的conversation_history插件群聊功能...\n")
        
        try:
            # 设置测试数据
            self.setup_test_data()
            
            # 运行各项测试
            tests = [
                ("自动配置创建", self.test_auto_config_creation),
                ("关键词触发功能", self.test_keyword_trigger),
                ("群聊消息处理", self.test_group_message_processing),
                ("私聊过滤功能", self.test_private_chat_filtering),
                ("配置持久化", self.test_config_persistence),
                ("消息关联功能", self.test_message_association),
                ("上下文构建功能", self.test_context_building)
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    success = test_func()
                    results.append((test_name, success))
                except Exception as e:
                    print(f"✗ {test_name} 测试失败: {e}")
                    results.append((test_name, False))
            
            # 总结测试结果
            print("\n=== 测试结果总结 ===")
            passed = sum(1 for _, success in results if success)
            total = len(results)
            
            for test_name, success in results:
                status = "✓ 通过" if success else "✗ 失败"
                print(f"{test_name}: {status}")
            
            print(f"\n总体结果: {passed}/{total} 项测试通过")
            
            if passed == total:
                print("🎉 所有测试通过！conversation_history插件群聊功能正常工作")
            else:
                print("⚠️  部分测试失败，需要检查相关功能")
            
        except Exception as e:
            print(f"\n✗ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.connection.close()


def main():
    """主函数"""
    # 数据库配置 - 请根据实际情况修改
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'your_password',
        'database': 'wechat_bot',
        'charset': 'utf8mb4'
    }
    
    print("开始测试优化后的conversation_history插件群聊功能...")
    
    tester = TestEnhancedGroupChat(db_config)
    tester.run_all_tests()


if __name__ == "__main__":
    main()

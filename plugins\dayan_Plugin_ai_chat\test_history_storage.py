"""
测试dayan_Plugin_ai_chat插件的历史记录存储功能
验证AI回复是否正确存储并建立关联关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import mysql.connector
from typing import Dict, List
import json
from unittest.mock import Mock


class TestAIChatHistoryStorage:
    def __init__(self, db_config: Dict):
        """初始化测试类"""
        self.connection = mysql.connector.connect(**db_config)
        
        # 模拟handler
        self.mock_handler = Mock()
        self.mock_handler.db = self._create_mock_db()
        self.mock_handler._log = self._mock_log
        
        # 导入并初始化插件
        from plugins.dayan_Plugin_ai_chat.plugin import AIChatPlugin
        self.plugin = AIChatPlugin(self.mock_handler)
    
    def _create_mock_db(self):
        """创建模拟数据库实例"""
        from plugins.dayan_Plugin_ai_chat.sql import AI_CHAT_SQL_FUNCTIONS
        from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS
        
        mock_db = Mock()
        mock_db.connection = self.connection
        
        # 绑定SQL函数
        for func_name, func in AI_CHAT_SQL_FUNCTIONS.items():
            setattr(mock_db, func_name, func.__get__(mock_db, mock_db.__class__))
        
        for func_name, func in CONVERSATION_HISTORY_SQL_FUNCTIONS.items():
            setattr(mock_db, func_name, func.__get__(mock_db, mock_db.__class__))
        
        # 模拟注册和卸载函数
        mock_db.register_plugin_functions = Mock()
        mock_db.unregister_plugin_functions = Mock()
        
        return mock_db
    
    def _mock_log(self, message, level="INFO"):
        """模拟日志输出"""
        print(f"[{level}] {message}")
    
    def setup_test_data(self):
        """设置测试数据"""
        print("=== 设置测试数据 ===")
        
        # 初始化表结构
        self.plugin.db.init_conversation_tables()
        print("✓ 对话记录表初始化完成")
        
        # 清理测试数据
        cursor = self.connection.cursor()
        cursor.execute("DELETE FROM conversation_history WHERE chat_name LIKE 'test_%'")
        cursor.execute("DELETE FROM ai_agent_profiles WHERE name LIKE 'test_%'")
        cursor.execute("DELETE FROM ai_keyword_rules WHERE chat_name LIKE 'test_%'")
        self.connection.commit()
        cursor.close()
        print("✓ 清理旧测试数据")
        
        # 创建测试AI智能体
        agent_id = self.plugin.db.create_ai_agent_profile(
            name="test_agent",
            description="测试AI智能体",
            api_key="test_key",
            url="http://test.com",
            model_type="gpt-3.5-turbo"
        )
        print(f"✓ 创建测试AI智能体 (ID: {agent_id})")
        
        return agent_id
    
    def test_user_message_storage(self):
        """测试用户消息存储功能"""
        print("\n=== 测试用户消息存储功能 ===")
        
        chat = "test_chat_group"
        chat_type = "group"
        messages = ["你好", "请帮我解答一个问题"]
        
        # 调用用户消息存储方法
        user_info = self.plugin._save_user_messages_to_history(chat, chat_type, messages)
        
        if user_info:
            print(f"✓ 用户消息存储成功 (ID: {user_info['user_message_id']})")
            print(f"  对话ID: {user_info['conversation_id']}")
            print(f"  合并内容: {user_info['combined_content']}")
            return user_info
        else:
            print("✗ 用户消息存储失败")
            return None
    
    def test_ai_reply_storage(self, user_info: Dict, agent_id: int):
        """测试AI回复存储功能"""
        print("\n=== 测试AI回复存储功能 ===")
        
        if not user_info:
            print("✗ 用户消息信息为空，无法测试AI回复存储")
            return None
        
        chat = "test_chat_group"
        chat_type = "group"
        ai_response = "你好！我是AI助手，很高兴为您服务。关于您的问题，我会尽力帮助您解答。"
        
        # 调用AI回复存储方法
        self.plugin._save_ai_reply_to_history(chat, chat_type, ai_response, user_info, agent_id)
        
        # 验证存储结果
        conversation = self.plugin.get_conversation_by_id(user_info['conversation_id'])
        
        if len(conversation) >= 2:
            print("✓ AI回复存储成功")
            for msg in conversation:
                role = "用户" if msg['message_type'] == 'user' else "AI助手"
                print(f"  {role}: {msg['message_content'][:50]}...")
            return True
        else:
            print("✗ AI回复存储失败或关联关系错误")
            return False
    
    def test_conversation_retrieval(self):
        """测试对话检索功能"""
        print("\n=== 测试对话检索功能 ===")
        
        chat = "test_chat_group"
        chat_type = "group"
        
        # 获取对话历史
        history = self.plugin.get_conversation_history(chat, chat_type, limit=10)
        
        print(f"获取到 {len(history)} 条对话记录:")
        for msg in history:
            role = "用户" if msg['message_type'] == 'user' else "AI助手"
            sender = msg.get('sender', '未知')
            print(f"  {role}({sender}): {msg['message_content'][:40]}...")
        
        return len(history) > 0
    
    def test_message_association(self):
        """测试消息关联关系"""
        print("\n=== 测试消息关联关系 ===")
        
        cursor = self.connection.cursor(dictionary=True)
        
        # 查询用户消息和对应的AI回复
        cursor.execute("""
            SELECT u.id as user_msg_id, u.message_content as user_content,
                   u.conversation_id, u.sender as user_sender,
                   b.id as bot_msg_id, b.message_content as bot_content,
                   b.reply_to_id
            FROM conversation_history u
            LEFT JOIN conversation_history b ON b.reply_to_id = u.id
            WHERE u.chat_name LIKE 'test_%' AND u.message_type = 'user'
            ORDER BY u.created_at DESC
            LIMIT 5
        """)
        
        associations = cursor.fetchall()
        cursor.close()
        
        print("消息关联关系验证:")
        success_count = 0
        for assoc in associations:
            if assoc['bot_msg_id']:
                print(f"✓ 用户消息 (ID: {assoc['user_msg_id']}) -> AI回复 (ID: {assoc['bot_msg_id']})")
                print(f"  对话ID: {assoc['conversation_id']}")
                print(f"  用户: {assoc['user_content'][:30]}...")
                print(f"  AI回复: {assoc['bot_content'][:30]}...")
                success_count += 1
            else:
                print(f"✗ 用户消息 (ID: {assoc['user_msg_id']}) 没有对应的AI回复")
            print()
        
        return success_count > 0
    
    def test_duplicate_prevention(self, user_info: Dict, agent_id: int):
        """测试重复存储防护"""
        print("\n=== 测试重复存储防护 ===")
        
        if not user_info:
            print("✗ 用户消息信息为空，无法测试重复存储防护")
            return False
        
        chat = "test_chat_group"
        chat_type = "group"
        ai_response = "这是一个重复的AI回复测试"
        
        # 第一次存储
        self.plugin._save_ai_reply_to_history(chat, chat_type, ai_response, user_info, agent_id)
        
        # 第二次存储相同内容（应该被防护）
        self.plugin._save_ai_reply_to_history(chat, chat_type, ai_response, user_info, agent_id)
        
        # 检查是否只有一条记录
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT COUNT(*) as count FROM conversation_history 
            WHERE message_content = %s AND message_type = 'bot'
        """, (ai_response,))
        
        count = cursor.fetchone()[0]
        cursor.close()
        
        if count == 1:
            print("✓ 重复存储防护正常工作")
            return True
        else:
            print(f"✗ 重复存储防护失败，发现 {count} 条重复记录")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试dayan_Plugin_ai_chat的历史记录存储功能...\n")
        
        try:
            # 设置测试数据
            agent_id = self.setup_test_data()
            
            # 测试用户消息存储
            user_info = self.test_user_message_storage()
            
            # 测试AI回复存储
            ai_reply_success = self.test_ai_reply_storage(user_info, agent_id)
            
            # 测试对话检索
            retrieval_success = self.test_conversation_retrieval()
            
            # 测试消息关联关系
            association_success = self.test_message_association()
            
            # 测试重复存储防护
            duplicate_prevention_success = self.test_duplicate_prevention(user_info, agent_id)
            
            # 总结测试结果
            print("\n=== 测试结果总结 ===")
            tests = [
                ("用户消息存储", user_info is not None),
                ("AI回复存储", ai_reply_success),
                ("对话检索", retrieval_success),
                ("消息关联关系", association_success),
                ("重复存储防护", duplicate_prevention_success)
            ]
            
            passed = sum(1 for _, success in tests if success)
            total = len(tests)
            
            for test_name, success in tests:
                status = "✓ 通过" if success else "✗ 失败"
                print(f"{test_name}: {status}")
            
            print(f"\n总体结果: {passed}/{total} 项测试通过")
            
            if passed == total:
                print("🎉 所有测试通过！dayan_Plugin_ai_chat历史记录存储功能正常工作")
            else:
                print("⚠️  部分测试失败，需要检查相关功能")
            
        except Exception as e:
            print(f"\n✗ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.connection.close()


def main():
    """主函数"""
    # 数据库配置 - 请根据实际情况修改
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'your_password',
        'database': 'wechat_bot',
        'charset': 'utf8mb4'
    }
    
    print("开始测试dayan_Plugin_ai_chat的历史记录存储功能...")
    
    tester = TestAIChatHistoryStorage(db_config)
    tester.run_all_tests()


if __name__ == "__main__":
    main()

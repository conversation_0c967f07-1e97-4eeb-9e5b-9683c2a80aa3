"""
conversation_history插件群聊功能快速验证脚本
用于快速测试自动配置创建和关键词触发功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import mysql.connector
from typing import Dict
import json


def test_database_connection(db_config: Dict) -> bool:
    """测试数据库连接"""
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        connection.close()
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def check_agent_4_exists(db_config: Dict) -> bool:
    """检查智能体4是否存在"""
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM ai_agent_profiles WHERE id = 4")
        agent = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if agent:
            print("✅ 智能体4存在")
            print(f"   名称: {agent.get('name', 'N/A')}")
            print(f"   模型: {agent.get('model_type', 'N/A')}")
            
            # 检查API配置
            if agent.get('api_key') and agent.get('url'):
                if 'your_api_key' not in agent.get('api_key', '') and 'your_api_url' not in agent.get('url', ''):
                    print("✅ API配置已设置")
                else:
                    print("⚠️  API配置需要更新（包含默认占位符）")
            else:
                print("⚠️  API配置不完整")
            
            return True
        else:
            print("❌ 智能体4不存在")
            print("   请运行: plugins/private_ai_assistant/setup_agent_4.sql")
            return False
            
    except Exception as e:
        print(f"❌ 检查智能体4失败: {e}")
        return False


def check_tables_exist(db_config: Dict) -> bool:
    """检查必要的数据库表是否存在"""
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        required_tables = [
            'conversation_config',
            'conversation_history',
            'ai_agent_profiles'
        ]
        
        missing_tables = []
        for table in required_tables:
            cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = '{table}'
            """)
            if cursor.fetchone()[0] == 0:
                missing_tables.append(table)
        
        cursor.close()
        connection.close()
        
        if not missing_tables:
            print("✅ 所有必要的数据库表都存在")
            return True
        else:
            print(f"❌ 缺少数据库表: {', '.join(missing_tables)}")
            print("   请运行插件初始化脚本")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        return False


def test_auto_config_creation(db_config: Dict) -> bool:
    """测试自动配置创建功能"""
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        test_chat = "test_auto_config_group"
        
        # 删除测试配置（如果存在）
        cursor.execute("""
            DELETE FROM conversation_config 
            WHERE chat_name = %s AND chat_type = 'group'
        """, (test_chat,))
        connection.commit()
        
        # 模拟自动配置创建
        cursor.execute("""
            INSERT INTO conversation_config 
            (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords)
            VALUES (%s, 'group', TRUE, 50, 24, 4, %s)
        """, (test_chat, json.dumps(["AI", "ai", "助手", "机器人"])))
        connection.commit()
        
        # 验证配置
        cursor.execute("""
            SELECT * FROM conversation_config 
            WHERE chat_name = %s AND chat_type = 'group'
        """, (test_chat,))
        config = cursor.fetchone()
        
        # 清理测试数据
        cursor.execute("""
            DELETE FROM conversation_config 
            WHERE chat_name = %s AND chat_type = 'group'
        """, (test_chat,))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        if config and config.get('ai_agent_id') == 4:
            print("✅ 自动配置创建功能正常")
            return True
        else:
            print("❌ 自动配置创建功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试自动配置创建失败: {e}")
        return False


def test_keyword_detection() -> bool:
    """测试关键词检测逻辑"""
    try:
        # 模拟关键词检测函数
        def should_trigger_ai(messages, trigger_keywords):
            for message in messages:
                for keyword in trigger_keywords:
                    if keyword.lower() in message.lower():
                        return True
            return False
        
        trigger_keywords = ["AI", "ai", "助手", "机器人"]
        
        test_cases = [
            (["AI你好"], True),
            (["请问助手"], True),
            (["机器人能帮我吗"], True),
            (["ai，今天天气怎么样"], True),
            (["今天天气不错"], False),
            (["大家好"], False),
            (["有人在吗"], False)
        ]
        
        all_passed = True
        for messages, expected in test_cases:
            result = should_trigger_ai(messages, trigger_keywords)
            if result == expected:
                print(f"✅ 关键词检测: '{messages[0]}' -> {result}")
            else:
                print(f"❌ 关键词检测: '{messages[0]}' -> 期望{expected}，实际{result}")
                all_passed = False
        
        if all_passed:
            print("✅ 关键词检测功能正常")
            return True
        else:
            print("❌ 关键词检测功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试关键词检测失败: {e}")
        return False


def test_message_storage(db_config: Dict) -> bool:
    """测试消息存储功能"""
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        test_chat = "test_message_storage"
        
        # 清理测试数据
        cursor.execute("""
            DELETE FROM conversation_history 
            WHERE chat_name = %s
        """, (test_chat,))
        connection.commit()
        
        # 插入测试消息
        cursor.execute("""
            INSERT INTO conversation_history 
            (chat_name, chat_type, sender, message_content, message_type, session_id)
            VALUES (%s, 'group', '测试用户', 'AI测试消息', 'user', 'test_session')
        """, (test_chat,))
        connection.commit()
        
        # 验证消息存储
        cursor.execute("""
            SELECT COUNT(*) FROM conversation_history 
            WHERE chat_name = %s
        """, (test_chat,))
        count = cursor.fetchone()[0]
        
        # 清理测试数据
        cursor.execute("""
            DELETE FROM conversation_history 
            WHERE chat_name = %s
        """, (test_chat,))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        if count > 0:
            print("✅ 消息存储功能正常")
            return True
        else:
            print("❌ 消息存储功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试消息存储失败: {e}")
        return False


def main():
    """主函数"""
    print("=== conversation_history插件群聊功能快速验证 ===\n")
    
    # 数据库配置 - 请根据实际情况修改
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'your_password',  # 请修改为实际密码
        'database': 'wechat_bot',     # 请修改为实际数据库名
        'charset': 'utf8mb4'
    }
    
    print("请确保已修改数据库配置信息！\n")
    
    # 运行检查项目
    checks = [
        ("数据库连接", lambda: test_database_connection(db_config)),
        ("数据库表结构", lambda: check_tables_exist(db_config)),
        ("智能体4配置", lambda: check_agent_4_exists(db_config)),
        ("自动配置创建", lambda: test_auto_config_creation(db_config)),
        ("关键词检测", lambda: test_keyword_detection()),
        ("消息存储", lambda: test_message_storage(db_config))
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n--- 检查: {check_name} ---")
        try:
            success = check_func()
            results.append((check_name, success))
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results.append((check_name, False))
    
    # 总结结果
    print("\n" + "="*50)
    print("检查结果总结:")
    print("="*50)
    
    passed = 0
    for check_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{check_name}: {status}")
        if success:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！conversation_history插件群聊功能已就绪")
        print("\n下一步:")
        print("1. 重启微信机器人服务")
        print("2. 在群聊中发送包含关键词的消息测试")
        print("3. 观察日志输出确认功能正常")
    else:
        print(f"\n⚠️  {total - passed} 项检查失败，请解决相关问题后重新测试")
        print("\n常见问题解决方案:")
        print("1. 数据库连接失败 -> 检查数据库配置和服务状态")
        print("2. 智能体4不存在 -> 运行 setup_agent_4.sql 脚本")
        print("3. 数据库表缺失 -> 运行插件初始化脚本")
        print("4. API配置问题 -> 更新智能体4的API密钥和URL")


if __name__ == "__main__":
    main()

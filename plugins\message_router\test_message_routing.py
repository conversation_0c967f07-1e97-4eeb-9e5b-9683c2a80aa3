"""
测试消息路由插件功能
验证私聊和群聊@消息的智能分发处理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from unittest.mock import Mock
from typing import Dict, List


class TestMessageRouting:
    def __init__(self):
        """初始化测试类"""
        # 模拟handler
        self.mock_handler = Mock()
        self.mock_handler._log = self._mock_log
        self.mock_handler.chat_type_cache = {}
        self.mock_handler.plugins = []
        
        # 导入并初始化消息路由插件
        from plugins.message_router.plugin import MessageRouterPlugin
        self.router_plugin = MessageRouterPlugin(self.mock_handler)
        
        # 创建模拟的其他插件
        self._create_mock_plugins()
    
    def _mock_log(self, message, level="INFO"):
        """模拟日志输出"""
        print(f"[{level}] {message}")
    
    def _create_mock_plugins(self):
        """创建模拟的其他插件"""
        # 模拟私聊AI插件
        self.mock_private_ai = Mock()
        self.mock_private_ai.__class__.__name__ = 'PrivateAIAssistantPlugin'
        self.mock_private_ai.on_messages = Mock(return_value="私聊AI回复")
        
        # 模拟群聊历史插件
        self.mock_conversation_history = Mock()
        self.mock_conversation_history.__class__.__name__ = 'ConversationHistoryPlugin'
        self.mock_conversation_history.on_messages = Mock(return_value="群聊AI回复")
        
        # 模拟其他插件
        self.mock_keyword_plugin = Mock()
        self.mock_keyword_plugin.__class__.__name__ = 'KeywordPlugin'
        self.mock_keyword_plugin.on_messages = Mock(return_value="关键词回复")
        
        # 设置插件列表
        self.mock_handler.plugins = [
            self.router_plugin,
            self.mock_private_ai,
            self.mock_conversation_history,
            self.mock_keyword_plugin
        ]
        
        # 手动设置插件引用
        self.router_plugin.private_ai_plugin = self.mock_private_ai
        self.router_plugin.conversation_history_plugin = self.mock_conversation_history
        self.router_plugin.other_plugins = [self.mock_keyword_plugin]
    
    def setup_test_data(self):
        """设置测试数据"""
        print("=== 设置测试数据 ===")
        
        # 设置聊天类型缓存
        self.mock_handler.chat_type_cache = {
            'private_user_1': 'friend',
            'private_user_2': 'friend',
            'group_chat_1': 'group',
            'group_chat_2': 'group',
            'tech_group': 'group'
        }
        print("✓ 聊天类型缓存设置完成")
    
    def test_private_chat_detection(self):
        """测试私聊检测功能"""
        print("\n=== 测试私聊检测功能 ===")
        
        test_cases = [
            ('private_user_1', True, "私聊用户1"),
            ('private_user_2', True, "私聊用户2"),
            ('group_chat_1', False, "群聊1"),
            ('tech_group', False, "技术群聊"),
            ('unknown_chat', True, "未知聊天（默认私聊）")
        ]
        
        success_count = 0
        for chat, expected, description in test_cases:
            result = self.router_plugin._is_private_chat(chat)
            if result == expected:
                print(f"✓ {description}: {result}")
                success_count += 1
            else:
                print(f"✗ {description}: 期望{expected}，实际{result}")
        
        if success_count == len(test_cases):
            print("✓ 私聊检测功能正常")
            return True
        else:
            print(f"✗ 私聊检测功能异常: {success_count}/{len(test_cases)} 通过")
            return False
    
    def test_at_message_detection(self):
        """测试@消息和AI呼叫检测功能"""
        print("\n=== 测试@消息和AI呼叫检测功能 ===")
        
        test_cases = [
            (["@AI 你好"], True, "@AI消息"),
            (["@助手 请帮忙"], True, "@助手消息"),
            (["@机器人 回答问题"], True, "@机器人消息"),
            (["@小明 AI能帮忙吗"], True, "@用户但提到AI"),
            (["AI，你好"], True, "AI开头呼叫"),
            (["助手，请帮忙"], True, "助手开头呼叫"),
            (["请问，AI能做什么"], True, "句中呼叫AI"),
            (["今天天气不错"], False, "普通聊天"),
            (["大家好"], False, "普通问候"),
            (["有人在吗"], False, "普通询问"),
            (["ai你好"], True, "小写ai呼叫"),
            (["机器人帮我查一下"], True, "机器人呼叫")
        ]
        
        success_count = 0
        for messages, expected, description in test_cases:
            result = self.router_plugin._is_at_message(messages)
            if result == expected:
                print(f"✓ {description}: {result}")
                success_count += 1
            else:
                print(f"✗ {description}: 期望{expected}，实际{result}")
        
        if success_count == len(test_cases):
            print("✓ @消息和AI呼叫检测功能正常")
            return True
        else:
            print(f"✗ @消息和AI呼叫检测功能异常: {success_count}/{len(test_cases)} 通过")
            return False
    
    def test_message_analysis(self):
        """测试消息分析功能"""
        print("\n=== 测试消息分析功能 ===")
        
        test_cases = [
            ('private_user_1', ["你好"], {
                'is_private': True,
                'needs_private_ai': True,
                'needs_group_ai': False,
                'needs_other_plugins': False
            }, "私聊消息"),
            
            ('group_chat_1', ["AI你好"], {
                'is_group': True,
                'has_ai_keywords': True,
                'needs_private_ai': False,
                'needs_group_ai': True,
                'needs_other_plugins': True
            }, "群聊AI关键词"),
            
            ('group_chat_1', ["@AI 帮忙"], {
                'is_group': True,
                'has_at': True,
                'needs_private_ai': False,
                'needs_group_ai': True,
                'needs_other_plugins': True
            }, "群聊@AI消息"),
            
            ('group_chat_1', ["今天天气不错"], {
                'is_group': True,
                'needs_private_ai': False,
                'needs_group_ai': False,
                'needs_other_plugins': True
            }, "群聊普通消息")
        ]
        
        success_count = 0
        for chat, messages, expected_fields, description in test_cases:
            analysis = self.router_plugin._analyze_message_type(chat, messages)
            
            # 检查期望的字段
            all_match = True
            for field, expected_value in expected_fields.items():
                if analysis.get(field) != expected_value:
                    print(f"✗ {description} - {field}: 期望{expected_value}，实际{analysis.get(field)}")
                    all_match = False
            
            if all_match:
                print(f"✓ {description}: 分析正确")
                success_count += 1
            else:
                print(f"✗ {description}: 分析错误")
        
        if success_count == len(test_cases):
            print("✓ 消息分析功能正常")
            return True
        else:
            print(f"✗ 消息分析功能异常: {success_count}/{len(test_cases)} 通过")
            return False
    
    def test_message_routing(self):
        """测试消息路由功能"""
        print("\n=== 测试消息路由功能 ===")
        
        # 重置模拟插件的调用记录
        self.mock_private_ai.on_messages.reset_mock()
        self.mock_conversation_history.on_messages.reset_mock()
        self.mock_keyword_plugin.on_messages.reset_mock()
        
        test_cases = [
            ('private_user_1', ["你好"], "私聊AI回复", "私聊消息路由"),
            ('group_chat_1', ["AI你好"], "群聊AI回复\n\n关键词回复", "群聊AI消息路由"),
            ('group_chat_1', ["今天天气不错"], "关键词回复", "群聊普通消息路由")
        ]
        
        success_count = 0
        for chat, messages, expected_response, description in test_cases:
            response = self.router_plugin.on_messages(chat, messages)
            
            if response == expected_response:
                print(f"✓ {description}: 路由正确")
                success_count += 1
            else:
                print(f"✗ {description}: 期望'{expected_response}'，实际'{response}'")
        
        # 验证插件调用情况
        print("\n插件调用验证:")
        print(f"私聊AI插件调用次数: {self.mock_private_ai.on_messages.call_count}")
        print(f"群聊历史插件调用次数: {self.mock_conversation_history.on_messages.call_count}")
        print(f"关键词插件调用次数: {self.mock_keyword_plugin.on_messages.call_count}")
        
        if success_count == len(test_cases):
            print("✓ 消息路由功能正常")
            return True
        else:
            print(f"✗ 消息路由功能异常: {success_count}/{len(test_cases)} 通过")
            return False
    
    def test_concurrent_message_handling(self):
        """测试并发消息处理"""
        print("\n=== 测试并发消息处理 ===")
        
        # 模拟同时收到私聊和群聊消息
        messages_batch = [
            ('private_user_1', ["私聊消息1"]),
            ('group_chat_1', ["AI群聊消息"]),
            ('private_user_2', ["私聊消息2"]),
            ('group_chat_1', ["普通群聊消息"])
        ]
        
        responses = []
        for chat, messages in messages_batch:
            response = self.router_plugin.on_messages(chat, messages)
            responses.append((chat, response))
        
        # 验证所有消息都得到了处理
        processed_count = sum(1 for _, response in responses if response is not None)
        
        print(f"处理的消息数量: {processed_count}/{len(messages_batch)}")
        
        for chat, response in responses:
            if response:
                print(f"✓ {chat}: {response[:30]}...")
            else:
                print(f"- {chat}: 无回复")
        
        if processed_count >= 3:  # 至少3条消息应该有回复
            print("✓ 并发消息处理功能正常")
            return True
        else:
            print("✗ 并发消息处理功能异常")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试消息路由插件功能...\n")
        
        try:
            # 设置测试数据
            self.setup_test_data()
            
            # 运行各项测试
            tests = [
                ("私聊检测功能", self.test_private_chat_detection),
                ("@消息和AI呼叫检测", self.test_at_message_detection),
                ("消息分析功能", self.test_message_analysis),
                ("消息路由功能", self.test_message_routing),
                ("并发消息处理", self.test_concurrent_message_handling)
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    success = test_func()
                    results.append((test_name, success))
                except Exception as e:
                    print(f"✗ {test_name} 测试失败: {e}")
                    results.append((test_name, False))
            
            # 总结测试结果
            print("\n=== 测试结果总结 ===")
            passed = sum(1 for _, success in results if success)
            total = len(results)
            
            for test_name, success in results:
                status = "✓ 通过" if success else "✗ 失败"
                print(f"{test_name}: {status}")
            
            print(f"\n总体结果: {passed}/{total} 项测试通过")
            
            if passed == total:
                print("🎉 所有测试通过！消息路由插件功能正常工作")
            else:
                print("⚠️  部分测试失败，需要检查相关功能")
            
        except Exception as e:
            print(f"\n✗ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("开始测试消息路由插件功能...")
    
    tester = TestMessageRouting()
    tester.run_all_tests()


if __name__ == "__main__":
    main()

-- 私聊AI助手插件 - 智能体4配置脚本
-- 用于设置默认的智能体4配置

-- 1. 检查智能体4是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '智能体4已存在'
        ELSE '智能体4不存在，需要创建'
    END as status
FROM ai_agent_profiles 
WHERE id = 4;

-- 2. 创建或更新智能体4配置
-- 请根据实际情况修改API密钥和URL
INSERT INTO ai_agent_profiles (
    id, 
    name, 
    description, 
    api_key, 
    url, 
    model_type,
    created_at,
    updated_at
) VALUES (
    4,
    'Default Private AI Assistant',
    '默认私聊AI助手 - 专门处理私聊消息，提供智能对话服务',
    'your_api_key_here',  -- 请替换为实际的API密钥
    'your_api_url_here',  -- 请替换为实际的API URL
    'gpt-3.5-turbo',      -- 模型类型，可选: gpt-3.5-turbo, dify_chatflow, dify_workflow, dify_agent
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    api_key = VALUES(api_key),
    url = VALUES(url),
    model_type = VALUES(model_type),
    updated_at = NOW();

-- 3. 验证智能体4配置
SELECT 
    id,
    name,
    description,
    model_type,
    CASE 
        WHEN api_key = 'your_api_key_here' THEN '⚠️ 需要配置真实API密钥'
        ELSE '✅ API密钥已配置'
    END as api_key_status,
    CASE 
        WHEN url = 'your_api_url_here' THEN '⚠️ 需要配置真实API URL'
        ELSE '✅ API URL已配置'
    END as url_status,
    created_at,
    updated_at
FROM ai_agent_profiles 
WHERE id = 4;

-- 4. 创建示例私聊AI配置（可选）
-- 为测试用户创建配置示例
INSERT INTO private_ai_config (
    user_name,
    enabled,
    ai_agent_id,
    context_limit,
    session_hours,
    personality
) VALUES (
    'demo_user',
    TRUE,
    4,
    20,
    24,
    '{"name": "AI助手", "style": "friendly", "features": ["helpful", "conversational", "context_aware"]}'
) ON DUPLICATE KEY UPDATE
    ai_agent_id = VALUES(ai_agent_id),
    context_limit = VALUES(context_limit),
    session_hours = VALUES(session_hours),
    personality = VALUES(personality),
    updated_at = NOW();

-- 5. 显示配置总结
SELECT '=== 智能体4配置完成 ===' as message;

SELECT 
    '智能体配置' as type,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(id, ':', name) SEPARATOR ', ') as details
FROM ai_agent_profiles 
WHERE id = 4;

SELECT 
    '私聊AI配置' as type,
    COUNT(*) as count,
    GROUP_CONCAT(user_name SEPARATOR ', ') as users
FROM private_ai_config;

-- 6. 配置检查清单
SELECT '=== 配置检查清单 ===' as message;

SELECT 
    '1. 智能体4存在' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as status
FROM ai_agent_profiles 
WHERE id = 4

UNION ALL

SELECT 
    '2. API密钥已配置' as check_item,
    CASE 
        WHEN COUNT(*) > 0 AND api_key != 'your_api_key_here' THEN '✅ 通过'
        WHEN COUNT(*) > 0 THEN '⚠️ 需要配置'
        ELSE '❌ 失败'
    END as status
FROM ai_agent_profiles 
WHERE id = 4

UNION ALL

SELECT 
    '3. API URL已配置' as check_item,
    CASE 
        WHEN COUNT(*) > 0 AND url != 'your_api_url_here' THEN '✅ 通过'
        WHEN COUNT(*) > 0 THEN '⚠️ 需要配置'
        ELSE '❌ 失败'
    END as status
FROM ai_agent_profiles 
WHERE id = 4

UNION ALL

SELECT 
    '4. 私聊AI表已创建' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'private_ai_config';

-- 7. 使用说明
SELECT '=== 使用说明 ===' as message;

SELECT 
'配置完成后，请执行以下步骤：

1. 修改API配置：
   UPDATE ai_agent_profiles 
   SET api_key = "真实的API密钥", url = "真实的API URL" 
   WHERE id = 4;

2. 重启微信机器人服务

3. 发送私聊消息测试功能

4. 查看日志确认插件正常工作：
   [私聊AI助手] 插件初始化完成
   [私聊AI助手] 为用户 XXX 生成回复

5. 可选：为特定用户自定义配置
   INSERT INTO private_ai_config (user_name, ai_agent_id, context_limit) 
   VALUES ("用户名", 4, 30);

注意事项：
- 确保智能体4的API配置正确
- 私聊AI助手只处理私聊消息，不影响群聊功能
- 插件优先级为100，会在关键词插件之前执行
- 支持上下文感知，会利用历史对话记录
' as instructions;

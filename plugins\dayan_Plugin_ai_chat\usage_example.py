"""
dayan_Plugin_ai_chat 历史记录功能使用示例
演示如何使用集成的历史记录存储功能
"""

from typing import Dict, List
import json
from datetime import datetime


class AIChatHistoryUsageExample:
    """AI聊天插件历史记录功能使用示例"""
    
    def __init__(self, plugin_instance):
        """
        初始化示例类
        
        Args:
            plugin_instance: dayan_Plugin_ai_chat插件实例
        """
        self.plugin = plugin_instance
    
    def example_basic_conversation(self):
        """示例：基本对话流程"""
        print("=== 基本对话流程示例 ===")
        
        # 模拟用户消息
        chat = "技术讨论群"
        messages = ["AI 你好，请介绍一下机器学习"]
        
        print(f"用户消息: {messages[0]}")
        
        # 插件处理消息（会自动存储用户消息和AI回复）
        ai_response = self.plugin.on_messages(chat, messages)
        
        if ai_response:
            print(f"AI回复: {ai_response[:100]}...")
            print("✓ 对话已自动存储到历史记录")
        else:
            print("✗ 未触发AI回复")
        
        return ai_response is not None
    
    def example_conversation_history_query(self):
        """示例：查询对话历史"""
        print("\n=== 对话历史查询示例 ===")
        
        chat = "技术讨论群"
        chat_type = "group"
        
        # 获取最近的对话历史
        history = self.plugin.get_conversation_history(chat, chat_type, limit=10)
        
        print(f"获取到 {len(history)} 条历史记录:")
        for i, msg in enumerate(history[-5:], 1):  # 显示最后5条
            role = "用户" if msg['message_type'] == 'user' else "AI助手"
            sender = msg.get('sender', '未知')
            timestamp = msg.get('created_at', '未知时间')
            content = msg['message_content'][:50] + "..." if len(msg['message_content']) > 50 else msg['message_content']
            
            print(f"  {i}. [{timestamp}] {role}({sender}): {content}")
        
        return len(history) > 0
    
    def example_conversation_by_id(self):
        """示例：根据对话ID查询完整对话"""
        print("\n=== 根据对话ID查询示例 ===")
        
        # 首先获取一个对话ID
        chat = "技术讨论群"
        chat_type = "group"
        history = self.plugin.get_conversation_history(chat, chat_type, limit=1)
        
        if not history:
            print("没有找到历史记录")
            return False
        
        # 获取第一条记录的对话ID
        conversation_id = history[0].get('conversation_id')
        if not conversation_id:
            print("历史记录中没有对话ID")
            return False
        
        # 根据对话ID获取完整对话
        conversation = self.plugin.get_conversation_by_id(conversation_id)
        
        print(f"对话ID {conversation_id} 的完整对话:")
        for msg in conversation:
            role = "用户" if msg['message_type'] == 'user' else "AI助手"
            print(f"  {role}: {msg['message_content'][:60]}...")
        
        return len(conversation) > 0
    
    def example_manual_storage(self):
        """示例：手动存储消息"""
        print("\n=== 手动存储消息示例 ===")
        
        chat = "测试群聊"
        chat_type = "group"
        messages = ["这是一条测试消息", "用于演示手动存储功能"]
        
        # 手动保存用户消息
        user_info = self.plugin._save_user_messages_to_history(chat, chat_type, messages)
        
        if user_info:
            print(f"✓ 用户消息已保存 (ID: {user_info['user_message_id']})")
            print(f"  对话ID: {user_info['conversation_id']}")
            
            # 模拟AI回复
            ai_response = "这是一个测试回复，用于演示手动存储功能。"
            
            # 手动保存AI回复
            self.plugin._save_ai_reply_to_history(chat, chat_type, ai_response, user_info, 1)
            print("✓ AI回复已保存并建立关联")
            
            return True
        else:
            print("✗ 用户消息保存失败")
            return False
    
    def example_batch_messages(self):
        """示例：批次消息处理"""
        print("\n=== 批次消息处理示例 ===")
        
        chat = "项目讨论群"
        chat_type = "group"
        
        # 模拟用户发送多条连续消息
        messages = [
            "AI 我想了解一下项目进度",
            "目前有哪些模块已经完成了？",
            "还有什么需要注意的问题吗？"
        ]
        
        print("用户连续发送的消息:")
        for i, msg in enumerate(messages, 1):
            print(f"  {i}. {msg}")
        
        # 插件处理批次消息
        ai_response = self.plugin.on_messages(chat, messages)
        
        if ai_response:
            print(f"\nAI回复: {ai_response[:100]}...")
            print("✓ 批次消息已自动存储并建立关联")
            return True
        else:
            print("✗ 未触发AI回复")
            return False
    
    def example_data_analysis(self):
        """示例：对话数据分析"""
        print("\n=== 对话数据分析示例 ===")
        
        # 获取多个聊天的历史记录
        chats = ["技术讨论群", "测试群聊", "项目讨论群"]
        total_messages = 0
        user_messages = 0
        ai_messages = 0
        
        for chat in chats:
            history = self.plugin.get_conversation_history(chat, "group", limit=100)
            total_messages += len(history)
            
            for msg in history:
                if msg['message_type'] == 'user':
                    user_messages += 1
                else:
                    ai_messages += 1
        
        print("对话数据统计:")
        print(f"  总消息数: {total_messages}")
        print(f"  用户消息: {user_messages}")
        print(f"  AI回复: {ai_messages}")
        
        if total_messages > 0:
            ai_ratio = (ai_messages / total_messages) * 100
            print(f"  AI回复比例: {ai_ratio:.1f}%")
        
        return total_messages > 0
    
    def example_cleanup_old_data(self):
        """示例：清理旧数据"""
        print("\n=== 清理旧数据示例 ===")
        
        # 清理30天前的对话记录
        days_to_keep = 30
        deleted_count = self.plugin.cleanup_old_conversations(days_to_keep)
        
        print(f"清理了 {deleted_count} 条超过 {days_to_keep} 天的对话记录")
        
        return True
    
    def example_error_handling(self):
        """示例：错误处理"""
        print("\n=== 错误处理示例 ===")
        
        # 测试空消息处理
        empty_response = self.plugin.on_messages("测试群", [])
        print(f"空消息处理结果: {empty_response}")
        
        # 测试无效对话ID查询
        invalid_conversation = self.plugin.get_conversation_by_id("invalid_id")
        print(f"无效对话ID查询结果: {len(invalid_conversation)} 条记录")
        
        # 测试无效聊天历史查询
        invalid_history = self.plugin.get_conversation_history("不存在的群聊", "group", 10)
        print(f"无效聊天历史查询结果: {len(invalid_history)} 条记录")
        
        return True
    
    def run_all_examples(self):
        """运行所有示例"""
        print("开始运行dayan_Plugin_ai_chat历史记录功能示例...\n")
        
        examples = [
            ("基本对话流程", self.example_basic_conversation),
            ("对话历史查询", self.example_conversation_history_query),
            ("根据对话ID查询", self.example_conversation_by_id),
            ("手动存储消息", self.example_manual_storage),
            ("批次消息处理", self.example_batch_messages),
            ("对话数据分析", self.example_data_analysis),
            ("清理旧数据", self.example_cleanup_old_data),
            ("错误处理", self.example_error_handling)
        ]
        
        results = []
        
        for name, example_func in examples:
            try:
                success = example_func()
                results.append((name, success))
                print(f"{'✓' if success else '✗'} {name} 示例完成\n")
            except Exception as e:
                print(f"✗ {name} 示例失败: {e}\n")
                results.append((name, False))
        
        # 总结结果
        print("=== 示例运行结果总结 ===")
        successful = sum(1 for _, success in results if success)
        total = len(results)
        
        for name, success in results:
            status = "✓ 成功" if success else "✗ 失败"
            print(f"{name}: {status}")
        
        print(f"\n总体结果: {successful}/{total} 个示例成功运行")
        
        if successful == total:
            print("🎉 所有示例都成功运行！")
        else:
            print("⚠️  部分示例运行失败，请检查相关配置")
        
        return successful == total


def main():
    """主函数 - 演示如何使用"""
    print("这是dayan_Plugin_ai_chat历史记录功能的使用示例")
    print("请确保:")
    print("1. 数据库连接正常")
    print("2. 插件已正确初始化")
    print("3. AI智能体配置正确")
    print("4. 关键词规则已设置")
    print("\n要运行实际示例，请:")
    print("1. 创建插件实例")
    print("2. 创建AIChatHistoryUsageExample实例")
    print("3. 调用run_all_examples()方法")
    
    # 示例代码框架
    print("\n示例代码:")
    print("""
# 假设已有插件实例
# plugin = AIChatPlugin(handler)

# 创建示例实例
# example = AIChatHistoryUsageExample(plugin)

# 运行所有示例
# example.run_all_examples()
    """)


if __name__ == "__main__":
    main()

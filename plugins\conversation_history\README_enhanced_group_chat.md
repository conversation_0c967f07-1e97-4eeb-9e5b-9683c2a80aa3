# conversation_history 插件群聊功能增强

## 概述

本次优化为conversation_history插件增加了智能群聊处理功能，实现了自动配置创建、关键词触发AI回复和完整的历史记录管理。

## 主要功能增强

### 🚀 自动配置创建
- **智能检测**：自动检测新群聊并创建默认配置
- **默认智能体**：新群聊自动使用智能体4
- **预设关键词**：默认关键词为["AI", "ai", "助手", "机器人"]
- **即插即用**：无需手动配置，群聊立即可用

### 🎯 关键词触发机制
- **智能识别**：检测消息中的触发关键词
- **灵活配置**：支持自定义关键词列表
- **精确匹配**：大小写不敏感的关键词匹配
- **上下文感知**：基于历史记录生成智能回复

### 📊 完整历史记录
- **消息存储**：所有群聊消息都会被保存
- **关联追踪**：用户消息与AI回复精确关联
- **上下文利用**：利用历史记录提供连贯对话
- **数据完整性**：支持消息批次和对话流程追踪

## 核心功能详解

### 1. 自动配置创建

#### 触发条件
当群聊首次发送消息时，系统会自动检测并创建配置：

```python
def _get_chat_config(self, chat_name: str, chat_type: str) -> Dict:
    # 如果配置不存在，自动创建
    if not config:
        default_ai_agent_id = 4 if chat_type == 'group' else None
        default_keywords = ["AI", "ai", "助手", "机器人"]
        
        # 创建默认配置
        self.db.upsert_conversation_config(
            chat_name=chat_name,
            chat_type=chat_type,
            enabled=True,
            context_limit=50,
            session_hours=24,
            ai_agent_id=default_ai_agent_id,
            trigger_keywords=json.dumps(default_keywords)
        )
```

#### 默认配置参数
```python
default_config = {
    'enabled': True,              # 启用对话记录
    'context_limit': 50,          # 上下文条数限制
    'session_hours': 24,          # 会话时长（小时）
    'ai_agent_id': 4,            # 使用智能体4
    'trigger_keywords': ["AI", "ai", "助手", "机器人"]  # 触发关键词
}
```

### 2. 关键词触发机制

#### 关键词检测
```python
def _should_trigger_ai(self, messages: List[str], trigger_keywords: List[str]) -> bool:
    for message in messages:
        for keyword in trigger_keywords:
            if keyword.lower() in message.lower():
                return True
    return False
```

#### 默认关键词
- **"AI"** - 大写AI
- **"ai"** - 小写ai  
- **"助手"** - 中文助手
- **"机器人"** - 中文机器人

#### 触发示例
```
✅ 触发AI回复的消息:
- "AI你好"
- "请问助手"
- "机器人能帮我吗"
- "ai，今天天气怎么样"

❌ 不触发AI回复的消息:
- "今天天气不错"
- "大家好"
- "有人在吗"
```

### 3. 智能回复生成

#### 上下文构建
系统会自动获取历史对话并构建智能提示词：

```python
def _build_context_prompt(self, context_messages: List[Dict], current_messages: List[str]) -> str:
    context_lines = []
    
    # 添加历史对话
    if context_messages:
        context_lines.append("=== 历史对话上下文 ===")
        for msg in context_messages:
            role = "用户" if msg['message_type'] == 'user' else "助手"
            context_lines.append(f"{role}: {msg['message_content']}")
        context_lines.append("=== 当前对话 ===")
    
    # 添加当前消息
    for msg in current_messages:
        context_lines.append(f"用户: {msg}")
    
    context_lines.append("\n请基于以上对话历史，给出合适的回复：")
    return "\n".join(context_lines)
```

#### 提示词示例
```
=== 历史对话上下文 ===
用户: AI你好
助手: 你好！我是AI助手，很高兴为您服务。
用户: 你能做什么
助手: 我可以回答问题、提供信息、协助解决问题等。

=== 当前对话 ===
用户: 请介绍一下Python编程

请基于以上对话历史，给出合适的回复：
```

### 4. 消息存储和关联

#### 用户消息存储
```python
def _save_user_messages(self, chat: str, chat_type: str, messages: List[str], config: Dict) -> Dict:
    # 生成对话ID和批次ID
    conversation_id = self.db.generate_conversation_id()
    message_batch_id = self.db.generate_batch_id() if len(messages) > 1 else None
    
    # 保存消息并返回关联信息
    message_id = self.db.save_user_message_with_batch(
        chat_name=chat,
        chat_type=chat_type,
        sender="群聊用户",
        message_content=" ".join(messages),
        conversation_id=conversation_id,
        message_batch_id=message_batch_id
    )
    
    return {
        'message_id': message_id,
        'conversation_id': conversation_id,
        'message_batch_id': message_batch_id
    }
```

#### AI回复存储
```python
# 保存AI回复并建立关联
reply_id = self.db.save_bot_reply(
    chat_name=chat,
    chat_type=chat_type,
    message_content=ai_response,
    conversation_id=user_message_info.get('conversation_id'),
    reply_to_id=user_message_info.get('message_id'),
    message_batch_id=user_message_info.get('message_batch_id')
)
```

## 工作流程

### 群聊消息处理流程
```
1. 接收群聊消息
2. 检查/创建群聊配置（自动使用智能体4）
3. 解析触发关键词
4. 检测是否包含关键词
   ├─ 不包含：保存消息到历史记录，结束
   └─ 包含：继续处理
5. 保存用户消息到历史记录
6. 获取历史上下文（最近50条）
7. 构建包含上下文的提示词
8. 调用智能体4生成AI回复
9. 保存AI回复并建立关联关系
10. 返回AI回复给用户
```

### 配置创建流程
```
1. 群聊首次发送消息
2. 系统检测配置不存在
3. 自动创建默认配置：
   - enabled: True
   - ai_agent_id: 4
   - context_limit: 50
   - trigger_keywords: ["AI", "ai", "助手", "机器人"]
4. 保存配置到数据库
5. 缓存配置以提高性能
```

## 数据库表结构

### conversation_config 表
```sql
CREATE TABLE conversation_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_name VARCHAR(255) NOT NULL,
    chat_type ENUM('group', 'private') NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    context_limit INT DEFAULT 50,
    session_hours INT DEFAULT 24,
    ai_agent_id BIGINT DEFAULT 4,
    trigger_keywords TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_chat (chat_name, chat_type)
);
```

### conversation_history 表
```sql
CREATE TABLE conversation_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_name VARCHAR(255) NOT NULL,
    chat_type ENUM('group', 'private') NOT NULL,
    sender VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    message_type ENUM('user', 'bot') NOT NULL,
    session_id VARCHAR(64) NOT NULL,
    conversation_id VARCHAR(64) NULL,
    reply_to_id BIGINT NULL,
    message_batch_id VARCHAR(64) NULL,
    is_batch_start BOOLEAN DEFAULT FALSE,
    is_batch_end BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_reply_to_id (reply_to_id),
    FOREIGN KEY (reply_to_id) REFERENCES conversation_history(id)
);
```

## 使用示例

### 基本使用
```
群聊场景：
用户: "AI你好"
系统: 自动创建配置 → 检测关键词 → 生成回复
AI: "你好！我是AI助手，很高兴为您服务！"

用户: "今天天气不错"
系统: 保存消息 → 无关键词 → 不回复

用户: "助手，请介绍一下自己"
系统: 检测关键词 → 获取上下文 → 生成回复
AI: "我是AI助手，可以回答问题、提供信息..."
```

### 配置查询
```sql
-- 查看群聊配置
SELECT * FROM conversation_config WHERE chat_type = 'group';

-- 查看特定群聊的对话历史
SELECT * FROM conversation_history 
WHERE chat_name = '群聊名称' AND chat_type = 'group'
ORDER BY created_at DESC LIMIT 20;
```

### 自定义配置
```sql
-- 修改群聊的触发关键词
UPDATE conversation_config 
SET trigger_keywords = '["AI", "助手", "小助手", "智能助手"]'
WHERE chat_name = '群聊名称' AND chat_type = 'group';

-- 修改上下文限制
UPDATE conversation_config 
SET context_limit = 30
WHERE chat_name = '群聊名称' AND chat_type = 'group';
```

## 监控和维护

### 日志监控
```
✅ 正常运行日志:
[对话记录] 为群聊 【技术讨论群】创建默认配置，使用智能体4
[对话记录] 【技术讨论群】触发关键词，生成AI回复 (回复ID: 123，关联消息ID: 122): 你好！我是AI助手...

❌ 错误日志:
[ERROR] [对话记录] 未找到AI智能体配置 ID: 4
[WARNING] [对话记录] 智能体4不存在，请运行setup_agent_4.sql脚本创建默认配置
```

### 性能监控
```sql
-- 查看活跃群聊
SELECT chat_name, COUNT(*) as message_count
FROM conversation_history 
WHERE chat_type = 'group' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY chat_name 
ORDER BY message_count DESC;

-- 查看AI回复统计
SELECT 
    DATE(created_at) as date,
    SUM(CASE WHEN message_type = 'user' THEN 1 ELSE 0 END) as user_messages,
    SUM(CASE WHEN message_type = 'bot' THEN 1 ELSE 0 END) as ai_replies
FROM conversation_history 
WHERE chat_type = 'group' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at);
```

## 注意事项

1. **智能体4配置**：确保数据库中存在ID为4的AI智能体配置
2. **插件优先级**：conversation_history优先级为200，在private_ai_assistant之后执行
3. **私聊过滤**：插件只处理群聊消息，私聊消息由private_ai_assistant处理
4. **关键词匹配**：大小写不敏感，支持中英文关键词
5. **上下文长度**：群聊默认使用50条上下文，可根据需要调整

## 扩展功能

### 自定义关键词
```sql
-- 为特定群聊设置自定义关键词
UPDATE conversation_config 
SET trigger_keywords = '["小助手", "智能助手", "机器人", "AI"]'
WHERE chat_name = '特定群聊' AND chat_type = 'group';
```

### 禁用特定群聊
```sql
-- 禁用特定群聊的AI功能
UPDATE conversation_config 
SET enabled = FALSE
WHERE chat_name = '特定群聊' AND chat_type = 'group';
```

### 调整智能体
```sql
-- 为特定群聊使用不同的智能体
UPDATE conversation_config 
SET ai_agent_id = 5
WHERE chat_name = '技术群聊' AND chat_type = 'group';
```

优化后的conversation_history插件为群聊提供了智能、自动化的AI助手服务，实现了真正的即插即用体验。
